'use client';

import { useInfiniteQuery, InfiniteData } from "@tanstack/react-query";
import { getGroups } from "@/lib/firebase/group";
import { GroupCard } from "./group-card";
import { useIntersectionObserver } from "@/lib/hooks/useIntersectionObserver";
import { useRef } from "react";
import Loading from '@/app/loading';
import { Group } from '@/lib/types/firebase';

const GROUPS_PER_PAGE = 12;

interface GroupListProps {
  searchQuery: string;
  filters: {
    categories: string[];
    platforms: string[];
  };
}

export function GroupList({ searchQuery, filters }: GroupListProps) {
  const loadMoreRef = useRef<HTMLDivElement>(null);

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    status
  } = useInfiniteQuery<Group[], Error, InfiniteData<Group[]>, [string, string, object], Date | null>({
    queryKey: ['groups', searchQuery, filters],
    initialPageParam: new Date(),
    queryFn: async ({ pageParam }) => {
      const groups = await getGroups({
        limit: GROUPS_PER_PAGE,
        lastGroupDate: pageParam,
        searchQuery,
        filters
      });
      return groups;
    },
    getNextPageParam: (lastPage: Group[]) => {
      if (lastPage.length < GROUPS_PER_PAGE) return undefined;
      return lastPage[lastPage.length - 1].createdAt;
    },
  });

  useIntersectionObserver({
    target: loadMoreRef,
    onIntersect: fetchNextPage,
    enabled: hasNextPage,
  });

  if (status === "pending") return <Loading />;
  if (status === "error") return <div>Bir hata oluştu</div>;

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {data.pages.some(page => page.length > 0) ? (
          data.pages.map((page: any[], i: any) => (
            page.map((group: Group) => (
              <GroupCard key={group.id} group={group} />
            ))
          ))
        ) : (
          <div className="col-span-full text-center py-12">
            <p className="text-lg text-muted-foreground">
              {searchQuery 
                ? `"${searchQuery}" için sonuç bulunamadı` 
                : filters.categories.length > 0 || filters.platforms.length > 0
                  ? 'Seçilen filtrelere uygun grup bulunamadı'
                  : 'Henüz hiç grup oluşturulmamış'
              }
            </p>
          </div>
        )}
      </div>
      
      <div ref={loadMoreRef} className="flex justify-center p-4">
        {isFetchingNextPage && <Loading />}
      </div>
    </div>
  );
}
