"use client";

import { useState, useEffect } from 'react';
import { User as FirebaseUser } from 'firebase/auth';
import { auth, db } from '@/lib/firebase';
import { onAuthStateChanged } from 'firebase/auth';
import { getDoc, doc } from 'firebase/firestore';
import { User } from '@/lib/types/firebase';

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    return onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));
        const userData = userDoc.data();
        
        setUser({
          ...firebaseUser,
          ...userData,
          role: userData?.role || 'user',
          username: userData?.username || firebaseUser.email?.split('@')[0] || '',
          displayName: userData?.displayName || firebaseUser.displayName || '',
          photoURL: userData?.photoURL || firebaseUser.photoURL || null,
        } as User);

        
      } else {
        setUser(null);
      }
      setLoading(false);
    });
  }, []);

  return { user, loading };
}