"use client";

import { useState, useEffect } from "react";
import { doc, getDoc, updateDoc, serverTimestamp } from "firebase/firestore";
import { db } from "@/lib/firebase";
import type { SuperAdmin, AdminPermission } from "@/lib/types/firebase";

interface UseSuperAdminReturn {
  isAdmin: boolean;
  adminData: SuperAdmin | null;
  loading: boolean;
  error: Error | null;
  hasPermission: (permission: AdminPermission) => boolean;
  updateLastAccess: () => Promise<void>;
}

export function useSuperAdmin(uid: string | undefined): UseSuperAdminReturn {
  const [isAdmin, setIsAdmin] = useState(false);
  const [adminData, setAdminData] = useState<SuperAdmin | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    async function checkAdminStatus() {
      if (!uid) {
        setIsAdmin(false);
        setAdminData(null);
        setLoading(false);
        return;
      }

      try {
        const adminDoc = await getDoc(doc(db, "superadmins", uid));
        
        if (adminDoc.exists()) {
          const data = adminDoc.data() as SuperAdmin;
          
          // Check if admin is active
          const isActiveAdmin = data.status === "active" && 
            (data.role === "superadmin" || data.role === "admin");
          
          setIsAdmin(isActiveAdmin);
          setAdminData(data);

          // IP whitelist kontrolü eklenebilir
          // const clientIP = await fetch('/api/getIP').then(r => r.text());
          // if (data.ipWhitelist.length > 0 && !data.ipWhitelist.includes(clientIP)) {
          //   throw new Error("Unauthorized IP address");
          // }
        } else {
          setIsAdmin(false);
          setAdminData(null);
        }
        setError(null);
      } catch (err) {
        console.error("Error checking admin status:", err);
        setError(err as Error);
        setIsAdmin(false);
        setAdminData(null);
      } finally {
        setLoading(false);
      }
    }

    setLoading(true);
    checkAdminStatus();
  }, [uid]);

  const hasPermission = (permission: AdminPermission): boolean => {
    if (!adminData) return false;
    if (adminData.role === 'superadmin') return true;
    return adminData.permissions.includes(permission) || adminData.permissions.includes('all');
  };

  const updateLastAccess = async (): Promise<void> => {
    if (!uid || !isAdmin) return;

    try {
      await updateDoc(doc(db, "superadmins", uid), {
        lastLogin: serverTimestamp(),
        lastUserAgent: navigator.userAgent,
        // lastIp: await fetch('/api/getIP').then(r => r.text())
      });
    } catch (error) {
      console.error("Error updating last access:", error);
    }
  };

  return { 
    isAdmin, 
    adminData, 
    loading, 
    error,
    hasPermission,
    updateLastAccess
  };
}