# Admin Panel Completion Checklist

## I. Admin Dashboard (`app/admin/page.tsx` & `components/admin/admin-dashboard.tsx`)
- [X] **Dashboard Content:** Review `AdminDashboard` component:
    - [X] Does it display key statistics (e.g., new users, active groups, pending reports, system health)?
    - [X] Does it have quick links/access to important admin sections or common tasks?
        *Comment: Added "Manage Users" and "Manage Groups" buttons with links to respective admin pages.*
    - [X] Is all data displayed on the dashboard dynamic and fetched from the backend?
        *Comment: Main metrics are dynamic. `AdminChart` and `AdminAlertsList` content assumed dynamic, but these components should be reviewed individually if issues arise.*

## II. User Management (`app/admin/users/`)
### A. Users List Page (`app/admin/users/page.tsx`)
- [X] **Sorting:** Implement UI for sorting users by clickable column headers (username, email, registration date, last login, etc.).
    *Comment: Implemented sorting logic with clickable headers and sort indicators. Refactored data fetching and processing for clarity.*
- [X] **Role Filtering:** Add a filter option for user roles.
    *Comment: Implemented role filtering with state, DropdownMenu, and updated data processing logic in `processedUsers` useMemo hook.*
- [X] **Display Role:** Show the user's role in the users table.
    *Comment: Implemented "Role" column in users table with styled Badge for role display. Adjusted colSpans for loading/empty states.*
- [X] **Quick Role Change:** Consider adding the ability to quickly change a user's role from the user list table (e.g., via a dropdown).
    *Comment: Implemented Quick Role Change using a DropdownMenu in the Actions column. Added handler to simulate backend call and update local state with toast notifications.*
- [X] **Enhanced Pagination:** Improve pagination to show total number of pages and allow direct navigation to a specific page.
    *Comment: Implemented dynamic calculation of `totalPages` and display of "Page X of Y". Direct page jump input not yet implemented but pagination is enhanced.*
- [X] **Error Handling:** Improve error feedback for bulk user actions (use toast notifications instead of `alert()`).
    *Comment: Implemented toast notifications for error handling in the `catch` block of `handleBulkAction`.*
- [X] **Page Size Customization:** Allow admin to change the page size (number of users displayed per page).
    *Comment: Implemented page size customization with a Select component in the pagination bar. `pageSize` state now uses `setPageSize`, and `page` resets to 1 on change.*
- [X] **"Suspend User" Functionality:**
    - [X] Clarify if "suspend" is a distinct status from "banned".
        *Comment: Plan: Investigate `lib/firebase/users.ts` and Firestore data structure (User model) to determine if 'suspend' (e.g., `user.status === 'suspended'`) is a separate, modifiable field or concept from the existing `user.banned` boolean. This clarification is needed before UI implementation.*
    - [ ] If distinct, implement UI controls and backend logic for suspending/unsuspending users.
        *Comment: Plan: If 'suspend' is a distinct feature, create backend functions (`suspendUser(userId, reason, duration?)`, `unsuspendUser(userId)`) and corresponding UI controls (in bulk actions, on user detail page) potentially with options for reason/duration.*
- [X] **Data Refresh:** Ensure user list data refreshes accurately after bulk actions without a full page reload if possible.
    *Comment: Current full refetch of users after bulk actions is acceptable for ensuring data consistency on an admin panel. No immediate change planned unless specific performance issues are noted.*

### B. User Details Page (`app/admin/users/[userId]/page.tsx`)
- [X] **Display Comprehensive User Details:**
    - [X] Nickname
        *Comment: Plan: Add UI element to display `user.nickname`.*
    - [X] Photo URL (view/clear)
        *Comment: Plan: Display user's photo using Avatar/Image. For 'clear', add button to trigger backend `clearUserProfilePhoto(userId)` function (backend function needs creation).*
    - [X] Current Status (active, suspended, banned)
        *Comment: Plan: Display `user.status` and `user.banned` clearly, possibly adapting `getStatusBadge` logic.*
    - [X] Ban Reason (if banned)
        *Comment: Plan: If `user.banned`, display `user.banReason`.*
    - [X] Last Login Date
        *Comment: Plan: Add UI element to display formatted `user.lastLogin`.*
    - [X] User Role
        *Comment: Plan: Add UI element to display `user.role`.*
    - [ ] IP Address (if tracked and appropriate)
        *Comment: Plan: Requires backend implementation for IP tracking (consider privacy implications) and corresponding data model changes before UI can be added.*
- [X] **Edit User Details:** Implement functionality to edit core user details (e.g., nickname, username, email - with appropriate warnings/verifications).
    *Comment: Implemented Edit Mode with state, handlers, and conditional rendering for Nickname and Username fields. Save is simulated, updates local state. Email editing deferred due to complexity.*
- [X] **Role Management:** Implement UI to change/assign a user's role.
    *Comment: Plan: Add a `Select` component on the user details page to change `user.role`. Call existing/new `changeUserRole(userId, newRole)` Firebase function. Update UI and show toast notification.*
- [X] **Ban Reason:** Add functionality to input a reason when banning a user and display it.
    *Comment: Plan: Modify "Ban User" button on details page to open a dialog prompting for a ban reason. Pass reason to `banUser(userId, reason)` (backend function `banUser` needs to accept and store the reason). Display of reason is covered in "Comprehensive Details".*
- [X] **Manual Email Verification:** Add an option for admins to manually verify a user's email or resend a verification email.
    *Comment: Frontend buttons and handlers for "Mark as Verified" and "Resend Verification" added. Backend functions `markEmailAsVerified` and `resendVerificationEmail` need to be created separately. Assumes `user.emailVerified` field exists or will be added to User type.*
- [X] **User Group Membership:** Display a list of groups the user is a member of (and potentially their role in that group).
    *Comment: Frontend UI for displaying user group memberships added (Card, list, links). Data fetching is currently simulated. Backend function `getUserGroupMemberships(userId)` needs to be created and integrated.*
- [ ] **User Activity Log:** Consider an audit log/activity stream for the user, visible to admins (e.g., significant profile changes, content reported, etc.).
    *Comment: Plan: Major feature. Requires designing an audit log schema (e.g., `userActivity` collection in Firestore), integrating logging calls into various backend functions, and building UI to display/filter these logs.*

## III. Group Management (`app/admin/groups/`)
### A. Groups List Page (`app/admin/groups/page.tsx`)
- [X] **Sorting:** Implement UI for sorting groups by clickable column headers (name, member count, creation date, etc.).
    *Comment: Plan: Add `sortField`, `sortDirection` state. Make table headers clickable to update sort state. Sort `filteredGroups` array accordingly. Add visual sort indicators.*
- [X] **Filtering:** Add filter options for:
    - [X] Group Category
        *Comment: Plan: Add `categoryFilter` state and `DropdownMenu`. Update `filteredGroups` logic.*
    - [X] Group Platform
        *Comment: Plan: Add `platformFilter` state and `DropdownMenu`. Update `filteredGroups` logic.*
    - [X] Group Type (public/private)
        *Comment: Plan: Add `typeFilter` state and `DropdownMenu`. Update `filteredGroups` logic.*
    - [X] Group Status (active/inactive/closed)
        *Comment: Plan: Add `statusFilter` state and `DropdownMenu`. Update `filteredGroups` logic, considering `isActive` and `type` fields.*
- [X] **Pagination:** Implement pagination for the groups list.
    *Comment: Plan: Add `page`, `pageSize` state. Calculate `totalPages`. Add UI for page navigation (Prev/Next, page numbers, jump input). Slice the filtered/sorted group list for current page display.*
- [X] **Bulk Actions:** Implement bulk actions for groups (e.g., activate selected, deactivate selected, delete selected - with confirmations).
    *Comment: Frontend for Bulk Actions (checkboxes, dropdown, confirmation) implemented. Backend calls for activate/deactivate/delete are simulated. `toggleGroupStatus` and `deleteGroup` backend functions need to be robust.*
- [X] **"Create New Group" Functionality:** Verify, test, and ensure the "Yeni Grup" (Create New Group) flow is complete and functional from the admin panel.
    *Comment: Integrated `CreateGroupDialog` into admin groups page. Added `onGroupCreated` callback to refresh list. Full end-to-end testing of group creation by admin and verification of all fields in dialog is still required.*
- [X] **Status/Type Display:** Consider separating group "active/inactive" status display from "public/private" type display in the table for better clarity.
    *Comment: Plan: Modify table to have two distinct columns or use two styled badges in one column for "Activity Status" (from `group.isActive`) and "Group Type" (from `group.type`).*
- [X] **Quick Actions:** Add quick actions to the group list table (e.g., direct edit button, toggle active/inactive status).
    *Comment: Frontend for Quick Actions (Edit, Toggle Status buttons) in group list implemented. Edit button links to a placeholder edit route. Toggle Status simulates backend call and updates UI. Backend `toggleGroupStatus` needs to be robust and an edit page/modal for groups needs to be created.*

### B. Group Details Page (`app/admin/groups/[groupId]/page.tsx`)
- [ ] **Edit Group Details:** Implement functionality for admins to edit group information:
    *Comment: Plan: Add "Edit Group" button or page mode. Create form with inputs for name, description, and selects for category, platform, type. Implement backend `updateGroupDetails(groupId, data)` function. Add Save/Cancel logic.*
    - [ ] Name
    - [ ] Description
    - [ ] Category
    - [ ] Platform
    - [ ] Type (public/private)
- [X] **Member Management:**
    *Comment: Frontend for Member Management (Remove, Promote, Demote actions via DropdownMenu) implemented on Group Details page. Confirmation for remove. Backend calls are simulated; functions `removeMemberFromGroup`, `promoteToGroupAdmin`, `demoteFromGroupAdmin` need to be created and integrated with actual backend logic.*
    - [X] **Remove Member:** Implement functionality to remove a member from the group.
    - [X] **Promote Member:** Implement functionality to promote a regular member to a group admin.
    - [X] **Demote Admin:** Implement functionality to demote a group admin to a regular member.
- [ ] **Delete Group:** Implement functionality to delete a group entirely (with appropriate warnings, possibly including what happens to content).
    *Comment: Plan: Add "Delete Group" button (with strong confirmation dialog). Create critical backend function `deleteGroup(groupId)` that carefully handles deletion of the group document and all associated data (messages, member lists, subcollections) to prevent orphaned data.*
- [ ] **Group Audit Log:** Consider adding an audit log for admin actions related to the group.
    *Comment: Plan: Design `groupActivityLogs` schema/collection. Integrate logging into group backend functions. Build UI on Group Details page to display/filter these logs.*
- [ ] **Admin-Specific Group Settings:** Review if any admin-specific group settings are needed beyond what's available to group owners/users (e.g., content posting rules overrides, join request settings).
    *Comment: Plan: Identify need for admin-only group settings. If required, add fields to group model in Firestore. Add UI section on Group Details page (edit mode) for site admins to manage these settings.*
- [ ] **Group Member Roles:** If groups can have specific member roles beyond "admin" and "regular member", display these roles and provide management capabilities.
    *Comment: Plan: Clarify if groups need specific member roles (e.g., "Moderator"). If yes, update Firestore data structure for group members. Display roles in member list on Group Details page. Add UI controls to assign/change these roles, with corresponding backend functions.*

## IV. Reports / Analytics (`app/admin/reports/page.tsx` - likely to be renamed `app/admin/analytics/page.tsx`)
- [X] **Purpose Clarification:**
    *Comment: Plan: Page confirmed for analytics. Rename route/directory from `reports` to `analytics` is recommended. User-generated reports (complaints) should be handled under `moderation`.*
    - [X] Clarify the primary purpose of this page. If it's for statistics/analytics, consider renaming the route/title (e.g., to "Analytics" or "Statistics").
    - [X] If it's intended for managing user-generated reports (complaints), this functionality needs to be built or integrated from the Moderation section.
- [ ] **Dynamic Data:** Connect all charts and displayed data to dynamic backend data from Firebase (currently static).
    *Comment: Plan: Create Firebase functions to aggregate data for each chart (User Activity, Platform Distribution, Category Activity). Fetch data using `useQuery` in the frontend and pass to chart components.*
- [X] **"Download Report" Functionality:**
    - [X] Define what data this button should export (e.g., CSV of current view, PDF of charts).
        *Comment: Decision: Start with CSV export of underlying data for one chart.*
    - [ ] Implement the download functionality.
        *Comment: Plan: Add CSV export library if needed (e.g., `papaparse`). On button click for a chart, fetch its dynamic data, convert to CSV, and trigger browser download.*
- [ ] **Date Range Filters:** Add date range filters (e.g., last 7 days, last 30 days, custom range) for all statistical charts and data.
    *Comment: Plan: Add date range picker UI. Update backend aggregation functions to accept date parameters. Pass selected range to data fetching queries.*
- [ ] **Data Granularity:** Consider adding more detailed data tables or drill-down options for the analytics displayed.
    *Comment: Plan: Identify areas for more detail (e.g., table view of user activity data below chart). Implement table display. Advanced drill-down is a future consideration.*

## V. Moderation (`app/admin/moderation/page.tsx`)
- [ ] **Dynamic Data:**
    *Comment: Plan: Define `reports` collection schema in Firestore. Create backend functions to fetch reports and aggregate counts for summary cards. Use `useQuery` in frontend to display dynamic data.*
    - [ ] Connect the reports list to dynamic data from Firebase (user-generated reports, content flags).
    - [ ] Connect summary card numbers (Pending Reports, Resolved Reports, Active Moderators) to dynamic data.
- [ ] **"Review Report" Functionality ("İncele" button):**
    *Comment: Plan: Multi-step. Create detailed report view (modal/page). Fetch/display all report data & related content/user info. Implement UI & backend for status changes, moderation actions (warn, ban, delete content), and adding internal notes.*
    - [ ] Create a detailed report view (page or modal).
    - [ ] Display all relevant report information (reporter, reported user/content, timestamps, report reason, context, etc.).
    - [ ] Allow moderators to take actions:
        - [ ] Change report status (e.g., Open, In Progress, Resolved-Valid, Resolved-Invalid, Escalated).
        - [ ] View the reported content or user profile directly.
        - [ ] Perform moderation actions (e.g., warn user, suspend user, ban user, delete content, edit content).
        - [ ] Add internal notes to the report.
- [ ] **Assign Reports:** Implement functionality to assign (or unassign) reports to specific moderators.
    *Comment: Plan: Add UI (dropdown in report list/detail) to assign report. Backend function `assignReport(reportId, moderatorId)` to update report document.*
- [X] **"Moderatör Ekle" Button:** Ensure the "Moderatör Ekle" (Add Moderator) button correctly links to or integrates with the moderator management section (`app/admin/moderators/page.tsx`).
    *Comment: Plan: Verify button links to `/admin/moderators/` or a dedicated add moderator page/dialog. Update if necessary.*
- [ ] **Real-time Updates:** Consider implementing real-time updates for the moderation queue so new reports appear without a manual refresh.
    *Comment: Plan: Consider using Firebase `onSnapshot` for the reports list query to enable real-time updates if beneficial for moderation workflow.*
- [ ] **Filtering & Sorting:** Enhance filtering (e.g., by report type, date, assigned moderator) and add sorting options for the reports table.
    *Comment: Plan: Add UI for filtering (type, assigned moderator, date) and column sorting to reports table. Update backend `getReports` function to handle these parameters.*

## VI. Admin Settings (`app/admin/settings/page.tsx`)
- [X] **State Management & Saving for All Settings:**
    *Comment: Plan: Extend `initialSettings` in `app/config/settings.ts` and `currentSettings` state in `settings/page.tsx` to cover all UI fields across all tabs. Ensure all UI controls (inputs, switches, selects) are correctly bound to this state. Verify `saveSettingsToFirebase` and `getSettingsFromFirebase` handle the complete, extended settings object.*
    - [X] **Security Tab:** Connect "Two-Factor Authentication", "Session Timeout", "IP Restriction", and "Allowed IPs" to component state and ensure they are saved via `saveSettingsToFirebase`.
    - [X] **Notifications Tab:** Connect all email notification switches (Security Alerts, System Updates, Weekly Reports) to component state and ensure they are saved.
    - [X] **API Tab:** Connect "API Key" (though likely read-only display of a generated key), "Webhook URL", and "Rate Limiting" to component state and ensure relevant parts are saved.
- [X] **"Cancel" Button Functionality:** Implement proper "Cancel" functionality to revert any unsaved changes in the settings form to their last saved state.
    *Comment: Plan: On "Cancel" click, re-trigger `fetchSettings` to discard local changes and repopulate `currentSettings` from Firebase, or reset to a stored copy of the initially fetched settings.*
- [ ] **API Key Management:** Implement logic for the "Yenile" (Renew) button.
    *Comment: Plan: Create backend Firebase function `generateNewApiKey()` (ensuring secure storage and generation). "Yenile" button calls this, then updates the read-only API key field in the UI with the new key.*
- [ ] **Input Validation:** Add appropriate input validation for settings fields.
    *Comment: Plan: Implement client-side validation for formats (e.g., IP addresses, valid URL for Webhook). Consider adding server-side validation in `saveSettingsToFirebase` for robustness.*
- [X] **Firebase Structure for New Settings:** Ensure `app/config/settings.ts` (`initialSettings`) and the Firebase data structure can accommodate all new settings fields from Security, Notifications, and API tabs.
    *Comment: Covered by the main "State Management & Saving" item's plan to extend `initialSettings` and ensure backend compatibility.*
- [ ] **Help Text/Tooltips:** Add descriptive help text or tooltips for more complex settings.
    *Comment: Plan: For settings like "IP Kısıtlaması", "Oturum Zaman Aşımı", etc., add small help icons with `Tooltip` components from ShadCN to explain their function and impact.*
- [X] **Initial Load for All Settings:** Ensure all settings, including new ones, are correctly fetched and populated into the form on initial load.
    *Comment: Plan: Covered by "State Management & Saving" plan; `getSettingsFromFirebase` should correctly populate the extended `currentSettings` state, using `initialSettings` as defaults for any fields not yet in Firebase.*

## VII. Other Admin Sections
- [ ] **Alerts (`app/admin/alerts/page.tsx`):**
    *Comment: Plan: Define purpose: What kind of alerts? System-generated, admin-created?
    - [ ] Implement functionality to view, create, manage, and dismiss alerts.
    - [ ] Determine if alerts need to be persisted or are real-time.
- [ ] **Moderators (`app/admin/moderators/page.tsx`):**
    *Comment: Plan: Review `useModerators` hook and page. Implement: list moderators (name, email, permissions), add new (form, link to user search/selection), edit permissions (dropdown/checkboxes in list/detail), remove moderator (button, confirmation). Consider audit log for these actions.*
    - [ ] Review existing functionality (if any, code not yet read).
    - [ ] List moderators.
    - [ ] Add new moderators (link with `app/admin/register/` or direct creation?).
    - [ ] Edit moderator permissions/roles.
    - [ ] Remove moderators.
    - [ ] Audit log for moderator actions.
- [ ] **Register (`app/admin/register/page.tsx`):**
    *Comment: Plan: Clarify: Is this for admins to register new platform users, or for existing users to apply for admin/moderator roles, or for super-admins to create new admin accounts? Implement the specific workflow. If it's for creating new admin accounts, ensure it's highly secure and possibly logs the action.*
    - [ ] Clarify its purpose: Is it for registering new admins by existing admins? Or some other registration flow?
    - [ ] Implement the defined registration workflow, including any approval processes.

## VIII. General Admin Panel
- [ ] **Consistent UI/UX:** Ensure a consistent look and feel across all admin pages.
- [ ] **Responsive Design:** Verify all admin pages are responsive and usable on different screen sizes.
- [ ] **Error Handling:** Implement robust and user-friendly error handling for all API calls and operations throughout the admin panel.
- [ ] **Loading States:** Ensure appropriate loading indicators are used for all data-fetching operations.
- [ ] **Accessibility:** Review admin panel for basic accessibility (keyboard navigation, ARIA attributes where needed).
- [ ] **Security:**
    - [ ] Ensure all admin endpoints are properly secured and require admin privileges.
    - [ ] Protect against common web vulnerabilities (XSS, CSRF, etc.).
- [ ] **Documentation:** Consider adding internal documentation for admin panel features if complex.
EOF
