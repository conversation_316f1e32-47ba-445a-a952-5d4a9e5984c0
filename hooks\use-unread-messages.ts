import { useState, useEffect } from 'react';
import { User } from 'firebase/auth';
import { db } from '@/lib/firebase';
import { collection, query, where, onSnapshot } from 'firebase/firestore';

export function useUnreadMessages(user: User | null) {
  const [unreadCount, setUnreadCount] = useState(0);

  useEffect(() => {
    if (!user) {
      
      return;
    }
    
    
    const q = query(
      collection(db, 'messages'),
      where('receiverId', '==', user.uid),
      where('read', '==', false)
    );

    try {
      const unsubscribe = onSnapshot(q, (snapshot) => {
        
        setUnreadCount(snapshot.size);
      }, (error) => {
        console.error("Error listening to messages:", error);
      });

      return () => unsubscribe();
    } catch (error) {
      console.error("Error setting up messages listener:", error);
    }
  }, [user]);

  return unreadCount;
} 