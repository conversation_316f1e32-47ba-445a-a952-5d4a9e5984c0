"use client";

import { useState, useEffect, useMemo } from "react"; // Import useMemo
import { collection, getDocs } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, UserCog, ChevronDown, Filter, Users, ChevronUp, MoreHorizontal } from "lucide-react"; // Import Users, ChevronUp, MoreHorizontal
import { Badge } from "@/components/ui/badge"; // Import Badge
import { useToast } from "@/components/ui/use-toast"; // Import useToast
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { formatDistanceToNow } from "date-fns";
import { tr } from "date-fns/locale";
import { User } from "../../types/user";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel, // Import DropdownMenuLabel
  DropdownMenuSeparator, // Import DropdownMenuSeparator
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Checkbox } from "@/components/ui/checkbox";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { banUser, unbanUser,deleteUser } from "@/lib/firebase/users";

export default function UsersPage() {
  const { toast } = useToast(); // Instantiate useToast
  const [users, setUsers] = useState<User[]>([]);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10); // Make pageSize a state
  const [sortField, setSortField] = useState<keyof User | ''>('createdAt'); // Allow '' for no sort
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [statusFilter, setStatusFilter] = useState<User['status'] | 'all' | 'banned'>('all'); // statusFilter can be 'banned'
  const [roleFilter, setRoleFilter] = useState<string>('all'); // Added roleFilter state
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);

  useEffect(() => {
    async function fetchUsers() {
      setLoading(true); // Set loading true at start
      try {
        const usersSnapshot = await getDocs(collection(db, "users"));
        const usersData = usersSnapshot.docs.map(doc => ({
          id: doc.id,
          email: doc.data().email || '',
          username: doc.data().username || '',
          status: doc.data().status || 'active',
          role: doc.data().role || 'user',
          nickname: doc.data().nickname,
          photoURL: doc.data().photoURL,
          createdAt: doc.data().createdAt?.toDate(),
          lastLogin: doc.data().lastLogin?.toDate(),
          banned: doc.data().banned || false, // Default to false
          banReason: doc.data().banReason
        })) as User[];
        setUsers(usersData);
      } catch (error) {
        console.error("Error fetching users:", error);
        toast({ title: "Hata", description: "Kullanıcılar yüklenirken bir sorun oluştu.", variant: "destructive" });
      } finally {
        setLoading(false);
      }
    }
    fetchUsers();
  }, [toast]); // Added toast to useEffect dependencies

  const handleSort = (field: keyof User) => {
    if (sortField === field) {
      setSortDirection(prevDirection => (prevDirection === 'asc' ? 'desc' : 'asc'));
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const processedUsers = useMemo(() => {
    let mutableUsers = [...users];

    // Filtering
    mutableUsers = mutableUsers.filter(user => {
      if (statusFilter !== 'all') {
        if (statusFilter === 'banned' && !user.banned) return false;
        if (statusFilter === 'active' && (user.banned || user.status !== 'active')) return false;
        if (statusFilter === 'suspended' && user.status !== 'suspended') return false;
      }
      if (roleFilter !== 'all' && user.role !== roleFilter) {
        return false;
      }
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        return (
          user.username?.toLowerCase().includes(query) ||
          user.email?.toLowerCase().includes(query) ||
          user.nickname?.toLowerCase().includes(query)
        );
      }
      return true;
    });

    // Sorting
    if (sortField) {
      mutableUsers.sort((a, b) => {
        const valA = a[sortField];
        const valB = b[sortField];
        let comparison = 0;
        if (valA === null || valA === undefined) comparison = -1;
        else if (valB === null || valB === undefined) comparison = 1;
        else if (valA instanceof Date && valB instanceof Date) {
          comparison = valA.getTime() > valB.getTime() ? 1 : (valA.getTime() < valB.getTime() ? -1 : 0);
        } else if (typeof valA === 'string' && typeof valB === 'string') {
          comparison = valA.localeCompare(valB);
        } else if (valA > valB) {
          comparison = 1;
        } else if (valA < valB) {
          comparison = -1;
        }
        return sortDirection === 'asc' ? comparison : -comparison;
      });
    }
    return mutableUsers;
  }, [users, statusFilter, roleFilter, searchQuery, sortField, sortDirection]);

  const handleQuickRoleChange = async (userId: string, newRole: string, currentRole?: string) => {
    if (currentRole === newRole) {
      toast({ title: "Bilgi", description: "Kullanıcı zaten bu rolde." });
      return;
    }
    try {
      // This is a placeholder for the actual backend call
      // For a real application, you would call a Firebase function here:
      // await functions.httpsCallable('changeUserRole')({ userId, newRole });
      console.log(`Simulating role change for user ${userId} to ${newRole}.`);

      setUsers(prevUsers =>
        prevUsers.map(u => (u.id === userId ? { ...u, role: newRole as User['role'] } : u))
      );
      toast({
        title: "Rol Değiştirildi (Simülasyon)",
        description: `Kullanıcının rolü ${newRole} olarak güncellendi.`,
      });
    } catch (error) {
      console.error("Error changing user role:", error);
      toast({
        title: "Hata",
        description: "Rol değiştirilirken bir hata oluştu.",
        variant: "destructive",
      });
    }
  };

  const paginatedUsers = useMemo(() => {
    const startIndex = (page - 1) * pageSize;
    return processedUsers.slice(startIndex, startIndex + pageSize);
  }, [processedUsers, page, pageSize]);

  const totalPages = Math.ceil(processedUsers.length / pageSize);

  const getStatusBadge = (user: User) => {
    let status = user.status;
    if (user.banned) status = 'banned';

    const statusConfig = {
      active: { color: 'bg-green-500', text: 'Aktif' },
      banned: { color: 'bg-red-500', text: 'Yasaklı' },
      suspended: { color: 'bg-yellow-500', text: 'Askıda' }
    };
    
    const config = statusConfig[status || 'active'];
    return (
      <div className="flex items-center gap-2">
        <div className={`h-2 w-2 rounded-full ${config.color}`}></div>
        <span className="text-sm">{config.text}</span>
      </div>
    );
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedUsers(processedUsers.map(user => user.id)); // Use processedUsers
    } else {
      setSelectedUsers([]);
    }
  };

  const handleSelectUser = (userId: string, checked: boolean) => {
    if (checked) {
      setSelectedUsers(prev => [...prev, userId]);
    } else {
      setSelectedUsers(prev => prev.filter(id => id !== userId));
    }
  };

  const handleBulkAction = async (action: 'ban' | 'unban' | 'delete') => {
    if (!selectedUsers.length) return;

    try {
      switch (action) {
        case 'ban':
          await Promise.all(selectedUsers.map(id => banUser(id)));
          break;
        case 'unban':
          await Promise.all(selectedUsers.map(id => unbanUser(id)));
          break;
        case 'delete':
          await Promise.all(selectedUsers.map(id => deleteUser(id)));
          break;
      }
      // Kullanıcıları yeniden yükle
      const usersSnapshot = await getDocs(collection(db, "users"));
      const usersData = usersSnapshot.docs.map(doc => ({
        id: doc.id,
        email: doc.data().email || '',
        username: doc.data().username || '',
        status: doc.data().status || 'active',
        role: doc.data().role || 'user',
        nickname: doc.data().nickname,
        photoURL: doc.data().photoURL,
        createdAt: doc.data().createdAt?.toDate(),
        lastLogin: doc.data().lastLogin?.toDate(),
        banned: doc.data().banned,
        banReason: doc.data().banReason
      })) as User[];
      setUsers(usersData);
      setSelectedUsers([]);
    } catch (error) {
      console.error('Bulk action error:', error);
      toast({
        title: "Toplu İşlem Hatası",
        description: "Kullanıcılar üzerinde işlem yapılırken bir hata oluştu. Lütfen tekrar deneyin.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Kullanıcılar</h1>
        {selectedUsers.length > 0 && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button>
                <UserCog className="w-4 h-4 mr-2" />
                Toplu İşlem ({selectedUsers.length})
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                    Seçilenleri Yasakla
                  </DropdownMenuItem>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Kullanıcıları Yasakla</AlertDialogTitle>
                    <AlertDialogDescription>
                      {selectedUsers.length} kullanıcıyı yasaklamak istediğinizden emin misiniz?
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>İptal</AlertDialogCancel>
                    <AlertDialogAction onClick={() => handleBulkAction('ban')}>
                      Yasakla
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>

              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                    Yasaklamaları Kaldır
                  </DropdownMenuItem>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Yasaklamaları Kaldır</AlertDialogTitle>
                    <AlertDialogDescription>
                      {selectedUsers.length} kullanıcının yasağını kaldırmak istediğinizden emin misiniz?
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>İptal</AlertDialogCancel>
                    <AlertDialogAction onClick={() => handleBulkAction('unban')}>
                      Yasağı Kaldır
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>

              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <DropdownMenuItem className="text-red-600" onSelect={(e) => e.preventDefault()}>
                    Seçilenleri Sil
                  </DropdownMenuItem>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Kullanıcıları Sil</AlertDialogTitle>
                    <AlertDialogDescription>
                      {selectedUsers.length} kullanıcıyı silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>İptal</AlertDialogCancel>
                    <AlertDialogAction onClick={() => handleBulkAction('delete')}>
                      Sil
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>

      <Card className="p-4">
        <div className="flex gap-4 mb-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Kullanıcı ara..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Filter className="w-4 h-4 mr-2" />
                Filtrele
                <ChevronDown className="w-4 h-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setStatusFilter('all')}>Tümü (Durum)</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setStatusFilter('active')}>Aktif Kullanıcılar</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setStatusFilter('banned')}>Yasaklı Kullanıcılar</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setStatusFilter('suspended')}>Askıya Alınmış</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Users className="w-4 h-4 mr-2" />
                Rol: {roleFilter === "all" ? "Tümü" : roleFilter.charAt(0).toUpperCase() + roleFilter.slice(1)}
                <ChevronDown className="w-4 h-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setRoleFilter('all')}>Tümü (Rol)</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setRoleFilter('user')}>Kullanıcı</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setRoleFilter('moderator')}>Moderatör</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setRoleFilter('admin')}>Admin</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </Card>

      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={processedUsers.length > 0 && selectedUsers.length === processedUsers.length}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead>
                <Button variant="ghost" onClick={() => handleSort('username')}>
                  Kullanıcı
                  {sortField === 'username' && (sortDirection === 'asc' ? <ChevronUp className="inline h-4 w-4 ml-1" /> : <ChevronDown className="inline h-4 w-4 ml-1" />)}
                </Button>
              </TableHead>
              <TableHead>
                <Button variant="ghost" onClick={() => handleSort('email')}>
                  E-posta
                  {sortField === 'email' && (sortDirection === 'asc' ? <ChevronUp className="inline h-4 w-4 ml-1" /> : <ChevronDown className="inline h-4 w-4 ml-1" />)}
                </Button>
              </TableHead>
              <TableHead>
                <Button variant="ghost" onClick={() => handleSort('createdAt')}>
                  Kayıt Tarihi
                  {sortField === 'createdAt' && (sortDirection === 'asc' ? <ChevronUp className="inline h-4 w-4 ml-1" /> : <ChevronDown className="inline h-4 w-4 ml-1" />)}
                </Button>
              </TableHead>
              <TableHead>
                <Button variant="ghost" onClick={() => handleSort('lastLogin')}>
                  Son Giriş
                  {sortField === 'lastLogin' && (sortDirection === 'asc' ? <ChevronUp className="inline h-4 w-4 ml-1" /> : <ChevronDown className="inline h-4 w-4 ml-1" />)}
                </Button>
              </TableHead>
              <TableHead>Durum</TableHead>
              <TableHead>Rol</TableHead>
              <TableHead className="text-right">İşlemler</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center"> {/* Adjusted colSpan to 8 */}
                  <div className="flex items-center justify-center py-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                </TableCell>
              </TableRow>
            ) : paginatedUsers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-4"> {/* Adjusted colSpan to 8 */}
                  Kullanıcı bulunamadı
                </TableCell>
              </TableRow>
            ) : (
              paginatedUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedUsers.includes(user.id)}
                      onCheckedChange={(checked) => handleSelectUser(user.id, checked as boolean)}
                    />
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={user.photoURL} />
                        <AvatarFallback>{user.username?.[0]}</AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{user.nickname || user.username}</p>
                        <p className="text-sm text-muted-foreground">@{user.username}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>
                    {user.createdAt ? formatDistanceToNow(user.createdAt, { addSuffix: true, locale: tr }) : '-'}
                  </TableCell>
                  <TableCell>
                    {user.lastLogin ? formatDistanceToNow(user.lastLogin, { addSuffix: true, locale: tr }) : '-'}
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(user)}
                  </TableCell>
                  <TableCell>
                    <Badge variant={user.role === "admin" ? "default" : (user.role === "moderator" ? "secondary" : "outline")}>
                      {user.role?.charAt(0).toUpperCase() + user.role?.slice(1) || "N/A"}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right space-x-2">
                    <Button variant="outline" size="sm" onClick={() => window.location.href = `/admin/users/${user.id}`}>Detaylar</Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">İşlemleri aç</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Rolü Değiştir</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        {['user', 'moderator', 'admin'].map((role) => (
                          <DropdownMenuItem
                            key={role}
                            disabled={user.role === role}
                            onClick={() => handleQuickRoleChange(user.id, role, user.role)}
                          >
                            {role.charAt(0).toUpperCase() + role.slice(1)}
                            {user.role === role && " (Mevcut)"}
                          </DropdownMenuItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </Card>

      {totalPages > 1 && ( // Conditionally render pagination
        <div className="flex items-center justify-between px-4 py-3 border-t">
          <div className="flex items-center gap-2"> {/* Added a div for page size select + text */}
            <span className="text-sm text-muted-foreground">Satır:</span>
            <Select value={pageSize.toString()} onValueChange={(value) => { setPageSize(Number(value)); setPage(1); }}>
              <SelectTrigger className="w-[70px]">
                <SelectValue placeholder={pageSize.toString()} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="text-sm text-muted-foreground">
            Sayfa {page} / {totalPages}
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(p => Math.max(1, p - 1))}
              disabled={page === 1}
            >
              Önceki
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(p => Math.min(totalPages, p + 1))}
              disabled={page === totalPages}
            >
              Sonraki
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}