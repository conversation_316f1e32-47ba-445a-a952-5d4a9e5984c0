"use client";

import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, UserPlus, Shield, Activity, CheckCircle } from "lucide-react";
import { ModeratorList } from "@/components/admin/moderators/moderator-list";
import { AddModeratorDialog } from "@/components/admin/moderators/add-moderator-dialog";
import { WithPermission } from "@/components/admin/with-permission";
import { useModerators } from "@/lib/hooks/useModerators";
import { MetricCard } from "@/components/admin/moderators/metric-card";

export default function ModeratorsPage() {
  const [showAddModerator, setShowAddModerator] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const { metrics } = useModerators();

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row items-center justify-between mb-2">
        <div>
          <h1 className="text-3xl font-bold">Moderatörler</h1>
          <p className="text-muted-foreground mt-1">
            Sistem moderatörlerini yönetin
          </p>
        </div>
        <WithPermission permission="users.manage">
          <Button onClick={() => setShowAddModerator(true)}>
            <UserPlus className="w-4 h-4 mr-2" />
            Moderatör Ekle
          </Button>
        </WithPermission>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <MetricCard
          title="Aktif Moderatörler"
          value={metrics?.activeCount || 0}
          icon={Shield}
          loading={!metrics}
        />
        <MetricCard
          title="Toplam İşlem"
          value={metrics?.totalActions || 0}
          icon={Activity}
          loading={!metrics}
        />
        <MetricCard
          title="Çözülen Raporlar"
          value={metrics?.resolvedReports || 0}
          icon={CheckCircle}
          loading={!metrics}
        />
      </div>

      <Card className="p-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Moderatör ara..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </Card>

      <ModeratorList searchQuery={searchQuery} />

      <AddModeratorDialog
        open={showAddModerator}
        onOpenChange={setShowAddModerator}
      />
    </div>
  );
} 