import { useState } from 'react';
import { adminCommands } from '@/lib/firebase/admin-commands';
import { toast } from 'sonner';

export function useAdminCommands(adminId: string) {
  const [loading, setLoading] = useState(false);

  const executeCommand = async <T extends keyof typeof adminCommands>(
    command: T,
    ...args: Parameters<typeof adminCommands[T]> extends [string, ...infer P] ? P : []
  ) => {
    setLoading(true);
    try {
      await adminCommands[command](adminId, ...args);
      toast.success('<PERSON><PERSON>lem başarıyla tamamlandı');
    } catch (error) {
      console.error(`<PERSON>rro<PERSON> executing command ${command}:`, error);
      toast.error('İşlem sırasında bir hata oluştu');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    executeCommand
  };
} 