import { doc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Group } from '../types/firebase';

export function useGroup() {
  const updateGroup = async (groupId: string, data: Partial<Group>) => {
    try {
      const groupRef = doc(db, 'groups', groupId);
      await updateDoc(groupRef, {
        ...data,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Grup güncellenirken hata:', error);
      throw error;
    }
  };

  return {
    updateGroup
  };
}