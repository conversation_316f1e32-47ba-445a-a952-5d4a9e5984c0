"use client";

import { Card } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { useState } from "react";

const categories = [
  { id: "fps", label: "FPS" },
  { id: "moba", label: "MOBA" },
  { id: "mmorpg", label: "MMORPG" },
  { id: "battle-royale", label: "Battle Royale" },
  { id: "strategy", label: "Strateji" },
];

const platforms = [
  { id: "pc", label: "PC" },
  { id: "playstation", label: "PlayStation" },
  { id: "xbox", label: "Xbox" },
  { id: "mobile", label: "Mobil" },
];

interface GroupFiltersProps {
  selectedCategories: string[];
  selectedPlatforms: string[];
  onCategoryChange: (category: string) => void;
  onPlatformChange: (platform: string) => void;
}

export function GroupFilters({
  selectedCategories,
  selectedPlatforms,
  onCategoryChange,
  onPlatformChange,
}: GroupFiltersProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <Card className="p-6">
      <div className="space-y-6">
        <div className="flex items-center justify-between cursor-pointer" onClick={() => setIsOpen(!isOpen)}>
          <h3 className="font-semibold">Kategoriler</h3>
          <span>{isOpen ? "−" : "+"}</span>
          {selectedCategories.length > 0 && (
            <Badge variant="secondary">
              {selectedCategories.length} seçili
            </Badge>
          )}
        </div>
        {isOpen && (
          <div className="space-y-3">
            {categories.map((category) => (
              <div key={category.id} className="flex items-center space-x-2">
                <Checkbox
                  id={category.id}
                  checked={selectedCategories.includes(category.id)}
                  onCheckedChange={() => onCategoryChange(category.id)}
                />
                <Label htmlFor={category.id}>{category.label}</Label>
              </div>
            ))}
          </div>
        )}

        <Separator />

        <div className="flex items-center justify-between cursor-pointer" onClick={() => setIsOpen(!isOpen)}>
          <h3 className="font-semibold">Platformlar</h3>
          <span>{isOpen ? "−" : "+"}</span>
          {selectedPlatforms.length > 0 && (
            <Badge variant="secondary">
              {selectedPlatforms.length} seçili
            </Badge>
          )}
        </div>
        {isOpen && (
          <div className="space-y-3">
            {platforms.map((platform) => (
              <div key={platform.id} className="flex items-center space-x-2">
                <Checkbox
                  id={platform.id}
                  checked={selectedPlatforms.includes(platform.id)}
                  onCheckedChange={() => onPlatformChange(platform.id)}
                />
                <Label htmlFor={platform.id}>{platform.label}</Label>
              </div>
            ))}
          </div>
        )}
      </div>
    </Card>
  );
}