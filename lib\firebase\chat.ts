import { db } from '@/lib/firebase';
import { 
  collection, 
  addDoc, 
  query, 
  orderBy, 
  limit, 
  onSnapshot,
  serverTimestamp,
  where,
  getDocs,
  Timestamp
} from 'firebase/firestore';

export interface ChatMessage {
  senderPhoto: string;
  id: string;
  content: string;
  senderId: string;
  senderName: string;
  createdAt: Date;
  groupId: string;
}

const convertTimestamp = (timestamp: any): Date => {
  if (!timestamp) return new Date();
  
  // Firestore Timestamp objesi ise
  if (timestamp.seconds) {
    return new Date(timestamp.seconds * 1000);
  }
  
  // Zaten Date objesi ise
  if (timestamp instanceof Date) {
    return timestamp;
  }
  
  // String ise
  if (typeof timestamp === 'string') {
    return new Date(timestamp);
  }
  
  return new Date();
};

export async function sendMessage(
  groupId: string, 
  content: string, 
  userId: string, 
  username: string,
  photoURL?: string
) {
  try {
    const messagesRef = collection(db, 'messages');
    await addDoc(messagesRef, {
      content,
      senderId: userId,
      senderName: username,
      senderPhoto: photoURL,
      groupId,
      createdAt: serverTimestamp()
    });
    return true;
  } catch (error) {
    console.error('Mesaj gönderme hatası:', error);
    throw error;
  }
}

export function subscribeToGroupMessages(
  groupId: string, 
  callback: (messages: ChatMessage[]) => void,
  errorCallback: (error: Error) => void
) {
  const messagesQuery = query(
    collection(db, 'messages'),
    where('groupId', '==', groupId),
    orderBy('createdAt', 'desc'),
    limit(100)
  );

  return onSnapshot(
    messagesQuery,
    (snapshot) => {
      const messages = snapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          content: data.content,
          senderId: data.senderId,
          senderName: data.senderName,
          senderPhoto: data.senderPhoto,
          groupId: data.groupId,
          createdAt: convertTimestamp(data.createdAt)
        } as ChatMessage;
      });
      
      callback(messages.reverse());
    },
    (error) => {
      console.error('Mesaj dinleme hatası:', error);
      errorCallback(error);
    }
  );
} 