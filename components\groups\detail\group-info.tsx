"use client";

import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import type { Group, GroupMember } from "@/lib/types/firebase";
import { formatDistanceToNow } from "date-fns";
import { tr } from "date-fns/locale";
import { Users, Shield, Hash, Globe2, Lock, Calendar, MessageSquare } from "lucide-react";
import { useGroupMembers } from '@/lib/hooks/useGroupMembers';
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import Link from "next/link";
import { GroupChat } from "./group-chat";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { useAuth } from "@/lib/hooks/useAuth";

interface GroupInfoProps {
  group: Group;
}

export function GroupInfo({ group }: GroupInfoProps) {
  const { user } = useAuth();
  const { members, loading, error } = useGroupMembers(group.id);
  const isMember = Boolean(user && group.members.includes(user.uid));

  const renderMemberItem = (member: GroupMember) => (
    <Link 
      href={`/users/${member.username}`}
      key={member.id} 
      className="flex items-center justify-between py-2 px-2 rounded-md hover:bg-muted/50 group transition-colors"
    >
      <div className="flex items-center gap-2">
        <div className="relative">
          <Avatar className="h-8 w-8">
            <AvatarImage src={member.photoURL || ''} alt={member.nickname} />
            <AvatarFallback className={member.role === 'admin' ? 'bg-primary/10' : 'bg-muted'}>
              {member.role === 'admin' ? (
                <Shield className="h-4 w-4 text-primary" />
              ) : (
                <Users className="h-4 w-4" />
              )}
            </AvatarFallback>
          </Avatar>
          {member.isOnline && (
            <span className="absolute bottom-0 right-0 w-2.5 h-2.5 bg-green-500 rounded-full border-2 border-background" />
          )}
        </div>
        <div>
          <div className="flex items-center gap-2">
            <p className="text-sm font-medium">{member.nickname}</p>
            {member.role === 'admin' && (
              <Badge variant="secondary" className="text-xs">Yönetici</Badge>
            )}
          </div>
          <p className="text-xs text-muted-foreground">
            {member.isOnline ? (
              <span className="text-green-500">Çevrimiçi</span>
            ) : (
              member.lastLogin ? (
                `Son görülme: ${formatDistanceToNow(new Date(member.lastLogin), { 
                  addSuffix: true, 
                  locale: tr 
                })}`
              ) : 'Hiç giriş yapmadı'
            )}
          </p>
        </div>
      </div>
    </Link>
  );

  return (
    <div className="grid grid-cols-12 gap-2 h-[calc(100vh-200px)]">
      {/* Sol Sidebar - Grup Bilgileri */}
      <div className="hidden md:block col-span-2 bg-background rounded-lg border overflow-y-auto">
        <div className="p-4 space-y-6">
         
        </div>
      </div>

      {/* Orta Alan - Grup Sohbeti */}
      <div className="col-span-12 md:col-span-7 bg-background rounded-lg border p-4">
        <GroupChat group={group} isMember={isMember} />
      </div>

      {/* Sağ Sidebar - Üye Listesi */}
      <div className="hidden md:block col-span-3 bg-background rounded-lg border p-4">
        <h3 className="text-sm font-semibold mb-3 px-2">
          Grup üyeleri ({group.memberCount})
        </h3>
        <div className="space-y-1">
          {loading ? (
            <p className="text-center text-muted-foreground">Yükleniyor...</p>
          ) : error ? (
            <p className="text-center text-red-500">Üyeler yüklenirken bir hata oluştu</p>
          ) : members.length === 0 ? (
            <p className="text-center text-muted-foreground">Henüz üye yok</p>
          ) : (
            members.map(renderMemberItem)
          )}
        </div>
      </div>
    </div>
  );
}