import { Card } from "@/components/ui/card";
import { LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { WithPermission } from "./with-permission";
import type { AdminPermission } from "@/lib/types/firebase";

interface AdminMetricCardProps {
  title: string;
  value: number;
  icon: LucideIcon;
  description?: string;
  variant?: "default" | "destructive";
  permission?: AdminPermission | AdminPermission[];
}

export function AdminMetricCard({
  title,
  value,
  icon: Icon,
  description,
  variant = "default",
  permission
}: AdminMetricCardProps) {
  const content = (
    <Card className={cn(
      "p-6",
      variant === "destructive" && "border-destructive/50 dark:border-destructive"
    )}>
      <div className="flex items-center justify-between">
        <Icon className={cn(
          "h-5 w-5",
          variant === "destructive" ? "text-destructive" : "text-muted-foreground"
        )} />
      </div>
      <div className="mt-4">
        <h3 className="text-2xl font-bold">{value.toLocaleString()}</h3>
        <p className="text-sm text-muted-foreground">{title}</p>
        {description && (
          <p className="text-xs text-muted-foreground mt-1">{description}</p>
        )}
      </div>
    </Card>
  );

  if (permission) {
    return (
      <WithPermission permission={permission}>
        {content}
      </WithPermission>
    );
  }

  return content;
}