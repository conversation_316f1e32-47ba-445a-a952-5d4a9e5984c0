import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const { code } = await request.json();

    const tokenResponse = await fetch('https://discord.com/api/oauth2/token', {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams({
        client_id: process.env.DISCORD_CLIENT_ID!,
        client_secret: process.env.DISCORD_CLIENT_SECRET!,
        code,
        grant_type: 'authorization_code',
        redirect_uri: `${process.env.NEXT_PUBLIC_BASE_URL}/api/auth/discord/callback`,
      }),
    });

    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text();
      console.error('Discord API Error:', {
        status: tokenResponse.status,
        statusText: tokenResponse.statusText,
        body: errorText
      });
      
      try {
        const errorData = JSON.parse(errorText);
        throw new Error(`Discord API Error: ${JSON.stringify(errorData)}`);
      } catch {
        throw new Error(`Discord API Error: ${errorText}`);
      }
    }

    const tokenData = await tokenResponse.json();
    // console.log('Token Response:', {
    //   status: tokenResponse.status,
    //   data: tokenData
    // });

    return NextResponse.json(tokenData);
  } catch (error: any) {
    console.error('Token error details:', {
      message: error.message,
      code: error.code,
      stack: error.stack
    });

    return NextResponse.json({ 
      error: error.message
    }, { status: 500 });
  }
} 