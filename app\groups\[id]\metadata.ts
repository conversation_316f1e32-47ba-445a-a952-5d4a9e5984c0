import { Metadata } from 'next'
import { getGroup } from '@/lib/firebase/group'

type Props = {
  params: { id: string }
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { id } = await params;
  const group = await getGroup(id)
  
  if (!group) {
    return {
      title: 'Grup Bulunamadı',
      description: 'Aradığınız grup bulunamadı.'
    }
  }

  return {
    title: group.name,
    description: group.description || 'Oyuncu Bul\'da bir oyun grubu',
    openGraph: {
      title: group.name,
      description: group.description,
      images: [
        {
          url: group.image || '/default-group-image.png',
          width: 1200,
          height: 630,
          alt: group.name
        }
      ]
    },    
    twitter: {
        card: 'summary_large_image',
        title: group.name, 
        description: group.description || 'Oyuncu Bul\'da bir oyun grubu',
        images: [group.image || '/default-group-image.png'], 
        creator: '@oyuncubul'
    }
  }
} 