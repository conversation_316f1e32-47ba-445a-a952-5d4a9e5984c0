"use client";

import { useEffect, useState } from "react";
import { doc, getDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { GroupDetailContent } from "./group-detail-content";
import { Skeleton } from "@/components/ui/skeleton";
import type { Group } from "@/lib/types/firebase";

interface GroupDetailWrapperProps {
  groupId: string;
}

export function GroupDetailWrapper({ groupId }: GroupDetailWrapperProps) {
  const [group, setGroup] = useState<Group | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchGroup() {
      try {
        const groupDoc = await getDoc(doc(db, "groups", groupId));
        if (groupDoc.exists()) {
          setGroup({
            id: groupDoc.id,
            ...groupDoc.data(),
            createdAt: groupDoc.data().createdAt?.toDate(),
          } as Group);
        } else {
          setError("Grup bulunamadı");
        }
      } catch (err) {
        setError("Grup yüklenirken bir hata oluştu");
        console.error("Error fetching group:", err);
      } finally {
        setLoading(false);
      }
    }

    fetchGroup();
  }, [groupId]);

  if (!groupId) {
    return (
      <div className="container max-w-6xl mx-auto py-8 px-4">
        <div className="text-center">
          <h1 className="text-2xl font-bold">Grup Bulunamadı</h1>
          <p className="text-muted-foreground mt-2">
            Geçersiz grup ID'si
          </p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="container max-w-6xl mx-auto py-8 px-4 space-y-8">
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  if (error || !group) {
    return (
      <div className="container max-w-6xl mx-auto py-8 px-4">
        <div className="text-center">
          <h1 className="text-2xl font-bold">Hata</h1>
          <p className="text-muted-foreground mt-2">
            {error || "Grup bulunamadı"}
          </p>
        </div>
      </div>
    );
  }

  return <GroupDetailContent group={group} />;
}