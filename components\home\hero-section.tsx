import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { GamepadIcon } from 'lucide-react';

export function HeroSection() {
  return (
    <section className="relative min-h-[90vh] flex items-center">
      <div className="absolute inset-0 bg-grid-white/[0.02] bg-grid" />
      <div className="absolute inset-0 bg-gradient-to-b from-background via-background/90 to-background" />
      
      <div className="container relative z-10 mx-auto px-4">
        <div className="flex flex-col items-center text-center max-w-6xl mx-auto">
          <GamepadIcon className="w-16 h-16 mb-8 text-primary animate-pulse" />
          <h1 className="text-4xl md:text-6xl font-bold tracking-tight mb-6 bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/50">
            Oyun Arkadaşını Bul
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-6xl">
            Türkiye'nin en büyük oyuncu eşleştirme platformu. Binlerce oyuncuyla tanış, 
            takımını kur ve oyun deneyimini zirveye taşı.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 w-full sm:w-auto">
            <Button size="lg" className="w-full sm:w-auto" asChild>
              <Link href="/auth/sign-up">
                Hemen Başla
              </Link>
            </Button>
            <Button size="lg" variant="outline" className="w-full sm:w-auto" asChild>
              <Link href="/groups">
                Grupları Keşfet
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}