"use client";

import { But<PERSON> } from "@/components/ui/button";
import { MoreHorizontal, Shield, Ban, Pencil } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useModerators } from "@/lib/hooks/useModerators";
import type { Moderator } from "@/lib/types/firebase";

interface ModeratorActionsProps {
  moderator: Moderator;
}

export function ModeratorActions({ moderator }: ModeratorActionsProps) {
  const { updateStatus } = useModerators();

  const handleStatusChange = async (status: Moderator['status']) => {
    await updateStatus.mutateAsync({
      moderatorId: moderator.id,
      status
    });
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm">
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem>
          <Pencil className="mr-2 h-4 w-4" /> Düzenle
        </DropdownMenuItem>
        <DropdownMenuItem>
          <Shield className="mr-2 h-4 w-4" /> Yetkileri Düzenle
        </DropdownMenuItem>
        <DropdownMenuItem 
          className="text-destructive"
          onClick={() => handleStatusChange(moderator.status === 'suspended' ? 'active' : 'suspended')}
        >
          <Ban className="mr-2 h-4 w-4" />
          {moderator.status === 'suspended' ? 'Askıyı Kaldır' : 'Askıya Al'}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
} 