"use client";

import { useState } from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Sheet, Sheet<PERSON>ontent, She<PERSON><PERSON>eader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import { Menu, MessageCircle, Users, Info, GamepadIcon } from "lucide-react";
import { useAuth } from "@/lib/hooks/useAuth";
import { signOut } from "firebase/auth";
import { auth } from "@/lib/firebase";
import { useUnreadMessages } from '@/hooks/use-unread-messages';

export function MobileNav() {
  const { user } = useAuth();
  const unreadCount = useUnreadMessages(user);
  const [isOpen, setIsOpen] = useState(false);
  const handleSignOut = async () => {
    try {
      await signOut(auth);
      setIsOpen(false);
    } catch (error) {
      console.error("Çıkış yapılırken hata oluştu:", error);
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="md:hidden">
          <Menu className="h-5 w-5" />
          <span className="sr-only">Menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="right" className="w-[300px] sm:w-[400px]">
        <SheetHeader>
          <SheetTitle className="flex items-center gap-2">
            <GamepadIcon className="h-5 w-5 text-primary" />
            Oyuncu Bul
          </SheetTitle>
        </SheetHeader>
        <nav className="flex flex-col space-y-4" aria-labelledby="mobile-menu-title">
          <Link
            href="/groups"
            onClick={() => setIsOpen(false)}
            className="text-sm font-medium p-2 rounded-md hover:bg-muted flex items-center gap-2"
          >
            <Users className="h-4 w-4" />
            Gruplar
          </Link>
          <Link
            href="/users"
            onClick={() => setIsOpen(false)}
            className="text-sm font-medium p-2 rounded-md hover:bg-muted flex items-center gap-2"
          >
            <Users className="h-4 w-4" />
            Üyeler
          </Link>
          <Link
            href="/about"
            onClick={() => setIsOpen(false)}
            className="text-sm font-medium p-2 rounded-md hover:bg-muted flex items-center gap-2"
          >
            <Info className="h-4 w-4" />
            Hakkında
          </Link>
          {!user ? (
            <>
              <Link href="/auth/sign-in" onClick={() => setIsOpen(false)}>
                <Button variant="outline" className="w-full">Giriş Yap</Button>
              </Link>
              <Link href="/auth/sign-up" onClick={() => setIsOpen(false)}>
                <Button className="w-full">Kayıt Ol</Button>
              </Link>
            </>
          ) : (
            <Button variant="outline" onClick={handleSignOut} className="w-full">
              Çıkış Yap
            </Button>
          )}
        </nav>
      </SheetContent>
    </Sheet>
  );
}