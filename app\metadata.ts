import type { Metadata } from 'next';

export const metadata: Metadata = {
  metadataBase: new URL('https://oyuncubul.com'),
  title: {
    default: 'Oyuncu Bul | Oyun Arkadaşını Bul',
    template: '%s | Oyuncu Bul'
  },
  description: 'Türkiye\'nin en büyük oyuncu eşleştirme platformu. Oyun arkadaşı bul, takımını kur, birlikte oyna!',
  keywords: ['oyun', 'gaming', 'oyuncu bulma', 'takım bulma', 'oyun arkadaşı'],
  authors: [{ name: 'Oyuncu Bul' }],
  creator: 'Oyuncu Bul',
  openGraph: {
    type: 'website',
    locale: 'tr_TR',
    url: 'https://oyuncubul.com',
    title: 'Oyuncu Bul | Oyun Arkadaşını Bul',
    description: 'Türkiye\'nin en büyük oyuncu eşleştirme platformu',
    siteName: 'Oyuncu Bul',
    images: [{
      url: '/og-image.png',
      width: 1200,
      height: 630,
      alt: '<PERSON>yuncu Bul'
    }],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Oyuncu Bul | Oyun Arkadaşını Bul',
    description: 'Türkiye\'nin en büyük oyuncu eşleştirme platformu',
    images: ['/twitter-image.png'],
    creator: '@oyuncubul'
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon-16x16.png',
    apple: '/apple-touch-icon.png',
  },
  manifest: '/site.webmanifest',
} 