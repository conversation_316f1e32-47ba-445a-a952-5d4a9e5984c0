import { db } from '@/lib/firebase';
import { 
  doc, 
  updateDoc, 
  deleteDoc, 
  collection, 
  query, 
  where, 
  getDocs,
  serverTimestamp,
  increment,
  writeBatch
} from 'firebase/firestore';
import { adminLogs } from './admin-logs';
import type { AdminCommand } from '@/lib/types/firebase';

export const adminCommands = {
  // Kullanıcı Komutları
  async banUser(adminId: string, userId: string, reason: string) {
    const userRef = doc(db, 'users', userId);
    await updateDoc(userRef, {
      banned: true,
      bannedAt: serverTimestamp(),
      bannedBy: adminId,
      banReason: reason
    });

    await adminLogs.createLog({
      adminId,
      action: 'BAN_USER',
      targetType: 'user',
      targetId: userId,
      details: { reason }
    });
  },

  async unbanUser(adminId: string, userId: string) {
    const userRef = doc(db, 'users', userId);
    await updateDoc(userRef, {
      banned: false,
      bannedAt: null,
      bannedBy: null,
      banReason: null
    });

    await adminLogs.createLog({
      adminId,
      action: 'UNBAN_USER',
      targetType: 'user',
      targetId: userId
    });
  },

  // Grup Komutları
  async closeGroup(adminId: string, groupId: string, reason: string) {
    const groupRef = doc(db, 'groups', groupId);
    await updateDoc(groupRef, {
      isClosed: true,
      closedAt: serverTimestamp(),
      closedBy: adminId,
      closeReason: reason
    });

    await adminLogs.createLog({
      adminId,
      action: 'CLOSE_GROUP',
      targetType: 'group',
      targetId: groupId,
      details: { reason }
    });
  },

  async deleteGroup(adminId: string, groupId: string, reason: string) {
    const batch = writeBatch(db);
    
    // Grup üyelerini güncelle
    const membersQuery = query(
      collection(db, 'users'),
      where('groups', 'array-contains', groupId)
    );
    const members = await getDocs(membersQuery);
    
    members.forEach(member => {
      batch.update(member.ref, {
        groups: member.data().groups.filter((id: string) => id !== groupId)
      });
    });

    // Grubu sil
    batch.delete(doc(db, 'groups', groupId));
    await batch.commit();

    await adminLogs.createLog({
      adminId,
      action: 'DELETE_GROUP',
      targetType: 'group',
      targetId: groupId,
      details: { reason }
    });
  },

  // Moderasyon Komutları
  async resolveReport(adminId: string, reportId: string, resolution: string) {
    const reportRef = doc(db, 'reports', reportId);
    await updateDoc(reportRef, {
      status: 'resolved',
      resolvedBy: adminId,
      resolvedAt: serverTimestamp(),
      resolution
    });

    await adminLogs.createLog({
      adminId,
      action: 'RESOLVE_REPORT',
      targetType: 'report',
      targetId: reportId,
      details: { resolution }
    });
  },

  // Sistem Komutları
  async updateSystemSettings(adminId: string, settings: Record<string, any>) {
    const settingsRef = doc(db, 'settings', 'system');
    await updateDoc(settingsRef, {
      ...settings,
      updatedAt: serverTimestamp(),
      updatedBy: adminId
    });

    await adminLogs.createLog({
      adminId,
      action: 'UPDATE_SETTINGS',
      targetType: 'system',
      targetId: 'settings',
      details: settings
    });
  }
}; 