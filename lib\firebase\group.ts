import { db } from '../firebase';
import { doc, updateDoc, arrayRemove, getDoc, collection, getDocs, orderBy, limit as fsLimit, query, startAfter, where } from 'firebase/firestore';
import { toast } from 'sonner';
import { Group } from '../types/firebase';

export async function leaveGroup(groupId: string, userId: string) {
  try {
    const groupRef = doc(db, 'groups', groupId);
    const groupSnap = await getDoc(groupRef);

    if (!groupSnap.exists()) {
      throw new Error('Grup bulunamadı');
    }

    const groupData = groupSnap.data();

    // Check if user is actually in the group
    if (!groupData.members.includes(userId)) {
      throw new Error('Bu grupta üye değilsiniz');
    }

    // Remove user from group members
    await updateDoc(groupRef, {
      members: arrayRemove(userId),
    });

    // Update user's groups list
    const userRef = doc(db, 'users', userId);
    await updateDoc(userRef, {
      groups: arrayRemove({
        id: groupId,
        name: groupData.name,
      }),
    });

    toast.success('<PERSON>ruptan başarıyla ayrıldınız');
    return true;
  } catch (error) {
    console.error('Gruptan ayrılma hatası:', error);
    if (error instanceof Error) {
      toast.error(`Hata: ${error.message}`);
    } else {
      toast.error('Gruptan ayrılırken bir hata oluştu');
    }
    return false;
  }
}

export async function toggleGroupStatus(groupId: string, isActive: boolean): Promise<void> {
  try {
    const groupRef = doc(db, 'groups', groupId);
    const groupSnap = await getDoc(groupRef);

    if (!groupSnap.exists()) {
      throw new Error('Grup bulunamadı');
    }

    await updateDoc(groupRef, {
      isActive,
      ...(isActive 
        ? { reopenedAt: new Date().toISOString() }
        : { closedAt: new Date().toISOString() }
      )
    });

    toast.success(isActive ? 'Grup başarıyla açıldı' : 'Grup başarıyla kapatıldı');
  } catch (error) {
    console.error('Grup durumu güncellenirken hata:', error);
    if (error instanceof Error) {
      toast.error(`Hata: ${error.message}`);
    } else {
      toast.error('Grup durumu güncellenirken bir hata oluştu');
    }
    throw error;
  }
}

export async function checkGroupAccess(groupId: string, userId: string | null): Promise<boolean> {
  try {
    const groupSnap = await getDoc(doc(db, 'groups', groupId));
    if (!groupSnap.exists()) return false;

    const groupData = groupSnap.data();
    
    // Kullanıcı yoksa erişimi engelle
    if (!userId) return false;

    // Admin kullanıcısı kontrolü
    const userDoc = await getDoc(doc(db, 'users', userId));
    const userData = userDoc.data();
    const isAdmin = userData?.role === 'admin' || userData?.role === 'superadmin';

    // Admin kullanıcıları her zaman erişebilir
    if (isAdmin) return true;
    console.log("groupData", groupData);

    // Grup kapalıysa ve kullanıcı admin değilse erişimi engelle
    if (!groupData.isActive && !groupData.admins?.includes(userId)) {
      return false;
    }

    return true;
  } catch (error) {
    console.log('Grup erişim kontrolü hatası:', error);
    return false;
  }
}

interface GetGroupsOptions {
  limit: number;
  lastGroupDate: Date | null;
  searchQuery?: string;
  filters?: {
    categories: string[];
    platforms: string[];
  };
}

export async function getGroups({ 
  limit, 
  lastGroupDate, 
  searchQuery = '', 
  filters = { categories: [], platforms: [] } 
}: GetGroupsOptions) {
  try {
    let groupQuery = query(
      collection(db, 'groups'),
      orderBy('createdAt', 'desc')
    );

    // Kategori filtresi
    if (filters.categories.length > 0) {
      groupQuery = query(groupQuery, where('category', 'in', filters.categories));
    }

    // Platform filtresi
    if (filters.platforms.length > 0) {
      groupQuery = query(groupQuery, where('platform', 'in', filters.platforms));
    }

    // Arama filtresi
    if (searchQuery) {
      groupQuery = query(
        groupQuery,
        where('name', '>=', searchQuery),
        where('name', '<=', searchQuery + '\uf8ff')
      );
    }

    // Sayfalama
    groupQuery = query(groupQuery, fsLimit(limit));
    if (lastGroupDate) {
      groupQuery = query(groupQuery, startAfter(lastGroupDate));
    }

    const snapshot = await getDocs(groupQuery);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Group[];
  } catch (error) {
    console.error('Gruplar yüklenirken hata:', error);
    return [];
  }
}

export async function getGroup(groupId: string) {
  const groupDoc = await getDoc(doc(db, 'groups', groupId))
  if (!groupDoc.exists()) return null
  
  return {
    id: groupDoc.id,
    ...groupDoc.data()
  } as Group
}
