"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { signInWithEmailAndPassword, signInWithPopup, GoogleAuthProvider } from "firebase/auth";
import { doc, getDoc, setDoc } from "firebase/firestore";
import { auth, db } from "@/lib/firebase";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { SocialButton } from "@/components/auth/social-button";

export default function SignInForm() {
  const router = useRouter();
  const { toast } = useToast();
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false);

  async function handleSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault();
    setError("");
    setLoading(true);

    const formData = new FormData(event.currentTarget);
    const email = formData.get("email") as string;
    const password = formData.get("password") as string;

    try {
      await signInWithEmailAndPassword(auth, email, password);
      
      toast({
        title: "Giriş başarılı",
        description: "Hoş geldiniz!",
      });

      router.push("/profile");
    } catch (err) {
      setError("E-posta veya şifre hatalı.");
      console.error(err);
    } finally {
      setLoading(false);
    }
  }

  async function handleGoogleSignIn() {
    setError("");
    setGoogleLoading(true);
    const provider = new GoogleAuthProvider();

    try {
      const result = await signInWithPopup(auth, provider);
      const user = result.user;

      // Check if user exists in Firestore
      const userDoc = await getDoc(doc(db, "users", user.uid));

      if (!userDoc.exists()) {
        // Create new user document if first time signing in
        const username = user.email?.split("@")[0]?.toLowerCase() || user.uid;
        await setDoc(doc(db, "users", user.uid), {
          id: user.uid,
          username,
          nickname: user.displayName || username,
          email: user.email,
          groups: [],
          pendingRequests: [],
          createdAt: new Date(),
          lastLogin: new Date(),
          isPublic: false,
          photoURL: user.photoURL || `https://api.dicebear.com/7.x/avataaars/svg?seed=${user.uid}`,
          linkedAccounts: {
            google: {
              data:{
                id: user.uid,
                email: user.email,
                name: user.displayName,
              },
              linked:true
            }
          }
        });
      } else {
        // Update existing user document with Google account info
        const userData = userDoc.data();
        const linkedAccounts = userData.linkedAccounts || {};
        linkedAccounts.google = {
          data:{
            id: user.uid,
            email: user.email,
            name: user.displayName,
          },
          linked:true
        };
        await setDoc(doc(db, "users", user.uid), { linkedAccounts }, { merge: true });
      }

      toast({
        title: "Giriş başarılı",
        description: "Google hesabınızla giriş yaptınız!",
      });

      router.push("/profile");
    } catch (err) {
      setError("Google ile giriş yapılırken bir hata oluştu.");
      console.error(err);
    } finally {
      setGoogleLoading(false);
    }
  }

  return (
    <div className="container max-w-md mx-auto py-16 px-4">
      <Card className="p-6">
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold">Giriş Yap</h1>
          <p className="text-muted-foreground mt-2">
            Hesabına giriş yap ve oyun arkadaşlarınla buluş.
          </p>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">E-posta</Label>
            <Input
              id="email"
              name="email"
              type="email"
              required
              placeholder="<EMAIL>"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="password">Şifre</Label>
            <Input
              id="password"
              name="password"
              type="password"
              required
              placeholder="********"
            />
          </div>

          <Button type="submit" className="w-full" disabled={loading}>
            {loading ? "Giriş yapılıyor..." : "Giriş Yap"}
          </Button>
        </form>

        <div className="relative my-6">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background px-2 text-muted-foreground">
              Veya şununla devam et
            </span>
          </div>
        </div>

        <SocialButton
          icon="google"
          onClick={handleGoogleSignIn}
          loading={googleLoading}
        >
          Google ile Giriş Yap
        </SocialButton>

        <div className="text-center space-y-2 mt-6">
          <p className="text-sm">
            Hesabın yok mu?{" "}
            <Link href="/auth/sign-up" className="text-primary hover:underline">
              Kayıt ol
            </Link>
          </p>
          <p className="text-sm">
            <Link href="/auth/forgot-password" className="text-primary hover:underline">
              Şifremi unuttum
            </Link>
          </p>
        </div>
      </Card>
    </div>
  );
}