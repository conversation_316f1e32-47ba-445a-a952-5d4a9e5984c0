import { HydrationBoundary, dehydrate } from '@tanstack/react-query';
import { getQueryClient, prefetchQueries } from '@/lib/utils/hydration';
import { AdminDashboard } from '@/components/admin/admin-dashboard';

export default async function AdminPage() {
  const queryClient = await prefetchQueries();
  const dehydratedState = dehydrate(queryClient);

  return (
    <HydrationBoundary state={dehydratedState}>
      <AdminDashboard />
    </HydrationBoundary>
  );
}