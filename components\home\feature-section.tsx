import { Card } from '@/components/ui/card';
import { Users, GamepadIcon, TrendingUp, Shield } from 'lucide-react';

const features = [
  {
    icon: Users,
    title: 'Geniş Topluluk',
    description: '10.000+ aktif oyuncu ve yüzlerce oyun grubu ile tanış'
  },
  {
    icon: GamepadIcon,
    title: 'Tüm Platformlar',
    description: 'PC, konsol ve mobil oyuncuları için özel gruplar'
  },
  {
    icon: TrendingUp,
    title: '<PERSON><PERSON><PERSON><PERSON>şleşme',
    description: 'Gelişmiş algoritma ile hızlı ve doğru eşleşmeler'
  },
  {
    icon: Shield,
    title: 'Güvenli Platform',
    description: 'Doğrulanmış profiller ve güvenli iletişim sistemi'
  }
];

export function FeatureSection() {
  return (
    <section className="py-24 px-4">
      <div className="container mx-auto max-w-6xl">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold mb-4">Neden Biz?</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Oyuncu Bul ile oyun deneyiminizi en üst seviyeye çıkarın
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <Card key={index} className="p-6 hover:shadow-lg transition-shadow">
                <Icon className="w-12 h-12 mb-4 text-primary" />
                <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                <p className="text-muted-foreground">{feature.description}</p>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
}