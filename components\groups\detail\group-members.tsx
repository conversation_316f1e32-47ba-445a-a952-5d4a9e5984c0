'use client';

import { useState, useEffect } from 'react';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Card } from '@/components/ui/card';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Shield, ShieldOff } from 'lucide-react';
import type { Group, User } from '@/lib/types/firebase';
import Link from 'next/link';
import { ClientMessageButton } from '@/components/user/client-message-button';
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

interface GroupMembersProps {
  group: Group;
  isAdmin: boolean;
  isMember: boolean;
}

export function GroupMembers({ group, isAdmin, isMember }: GroupMembersProps) {
  const [members, setMembers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [admins, setAdmins] = useState<string[]>(group.admins);

  useEffect(() => {
    async function fetchMembers() {
      try {
        const memberPromises = group.members.map(async (memberId) => {
          const userDoc = await getDoc(doc(db, 'users', memberId));
          return { id: userDoc.id, ...userDoc.data() } as User;
        });

        const memberData = await Promise.all(memberPromises);
        setMembers(memberData);
      } catch (error) {
        console.error('Error fetching members:', error);
      } finally {
        setLoading(false);
      }
    }

    if (isMember) {
      fetchMembers();
    }
  }, [group.members, isMember]);

  const toggleAdmin = async (memberId: string, isCurrentlyAdmin: boolean) => {
    try {
      const newAdmins = isCurrentlyAdmin
        ? admins.filter(id => id !== memberId)
        : [...admins, memberId];

      await updateDoc(doc(db, 'groups', group.id), {
        admins: newAdmins
      });

      setAdmins(newAdmins);
      toast.success(isCurrentlyAdmin ? 'Yönetici yetkisi alındı' : 'Yönetici yetkisi verildi');
    } catch (error) {
      console.error('Error updating admin status:', error);
      toast.error('Bir hata oluştu');
    }
  };

  if (!isMember) {
    return (
      <Card className="p-6">
        <p className="text-center text-muted-foreground">
          Üye listesini görmek için gruba katılmanız gerekiyor.
        </p>
      </Card>
    );
  }

  if (loading) {
    return (
      <Card className="p-6">
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex items-center gap-4">
              <div className="w-10 h-10 rounded-full bg-muted animate-pulse" />
              <div className="flex-1">
                <div className="h-4 bg-muted rounded w-1/4 animate-pulse" />
                <div className="h-3 bg-muted rounded w-1/3 mt-2 animate-pulse" />
              </div>
            </div>
          ))}
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6">
      <div className="space-y-4">
        {members.map((member) => (
          <div key={member.id} className="flex items-center justify-between hover:bg-muted/50 p-2 rounded-md transition-colors">
            <Link href={`/users/${member.username}`}>
              <div className="flex items-center gap-4">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={member.photoURL || ''} />
                  <AvatarFallback>{member.nickname?.[0]}</AvatarFallback>
                </Avatar>
                <div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{member.nickname}</span>
                    {group.admins.includes(member.id as string) && (
                      <Badge variant="secondary" className="text-xs">
                        <Shield className="w-3 h-3 mr-1" />
                        Admin
                      </Badge>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground">@{member.username}</p>
                </div>
              </div>
            </Link>
            <div className="flex items-center gap-2">
              {isAdmin && member.id !== group.ownerId && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => toggleAdmin(member.id as string, admins.includes(member.id as string))}
                  className="text-muted-foreground hover:text-foreground"
                >
                  {admins.includes(member.id as string) ? (
                    <ShieldOff className="w-4 h-4" />
                  ) : (
                    <Shield className="w-4 h-4" />
                  )}
                </Button>
              )}
              <ClientMessageButton 
                userId={member.id as string} 
                username={member.username || member.nickname || ''} 
              />
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
}
