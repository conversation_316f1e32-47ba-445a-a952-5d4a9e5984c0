"use client";

import { useState } from 'react';
import { adminLogs, adminOperations } from '@/lib/firebase/admin-logs';
import type { AdminLog } from '@/lib/types/firebase';

export function useAdminLogs() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const getLogs = async (filters?: Parameters<typeof adminLogs.getLogs>[0]) => {
    setLoading(true);
    try {
      const logs = await adminLogs.getLogs(filters);
      return logs;
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const banUser = async (adminId: string, userId: string, reason: string) => {
    setLoading(true);
    try {
      return await adminOperations.banUser(adminId, userId, reason);
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    getLogs,
    banUser,
    loading,
    error
  };
} 