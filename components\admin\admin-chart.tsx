"use client";

import { useQuery } from "@tanstack/react-query";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { adminService } from "@/lib/services/admin";

export function AdminChart() {
  const { data, isLoading } = useQuery({
    queryKey: ['admin-activity'],
    queryFn: () => adminService.getActivityData(7),
    refetchInterval: 5 * 60 * 1000, // 5 dakikada bir otomatik güncelle
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[300px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data}>
          <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
          <XAxis 
            dataKey="date" 
            className="text-xs" 
            tick={{ fill: "hsl(var(--foreground))" }}
          />
          <YAxis 
            className="text-xs" 
            tick={{ fill: "hsl(var(--foreground))" }}
          />
          <Tooltip
            contentStyle={{
              backgroundColor: "hsl(var(--background))",
              border: "1px solid hsl(var(--border))",
              borderRadius: "var(--radius)",
            }}
            labelClassName="text-foreground"
          />
          <Line
            name="Aktif Kullanıcılar"
            type="monotone"
            dataKey="users"
            stroke="hsl(var(--primary))"
            strokeWidth={2}
            dot={false}
          />
          <Line
            name="Yeni Gruplar"
            type="monotone"
            dataKey="groups"
            stroke="hsl(var(--secondary))"
            strokeWidth={2}
            dot={false}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}