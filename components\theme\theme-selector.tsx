"use client";

import { Sun } from "lucide-react";
import { useTheme } from "next-themes";

const themes = [
  {
    name: "black",
    label: "Siyah",
    icon: Sun,
    colors: ["#ffffff", "#000000", "#f5f5f5"]
  },
  {
    name: "rose",
    label: "<PERSON>ü<PERSON>",
    icon: Sun,
    colors: ["#fff1f2", "#e11d48", "#fecdd3"]
  },
  {
    name: "green",
    label: "Yeşil",
    icon: Sun,
    colors: ["#f0fdf4", "#16a34a", "#dcfce7"]
  },
  {
    name: "purple",
    label: "Mor",
    icon: Sun,
    colors: ["#faf5ff", "#9333ea", "#f3e8ff"]
  },
  {
    name: "orange",
    label: "<PERSON><PERSON><PERSON>",
    icon: Sun,
    colors: ["#fff7ed", "#ea580c", "#fed7aa"]
  },
  {
    name: "blue",
    label: "<PERSON><PERSON>",
    icon: Sun,
    colors: ["#eff6ff", "#2563eb", "#bfdbfe"]
  }
];

export function ThemeSelector() {
  const { theme, setTheme } = useTheme();
  const currentTheme = theme?.replace('-dark', '');

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
      {themes.map((item) => (
        <button
          key={item.name}
          onClick={() => {
            const isDark = theme?.includes('-dark');
            setTheme(isDark ? `${item.name}-dark` : item.name);
          }}
          className={`relative flex flex-col items-center gap-2 p-4 rounded-lg border-2 transition-all duration-200 ${
            currentTheme === item.name 
              ? "border-primary bg-primary/5" 
              : "border-muted hover:border-primary/50"
          }`}
        >
          <div className="flex items-center justify-center w-12 h-12 rounded-full" 
               style={{ backgroundColor: item.colors[0] }}>
            <item.icon className="w-6 h-6" 
                      style={{ color: item.colors[1] }} />
          </div>
          <div className="flex gap-1.5">
            {item.colors.map((color, i) => (
              <div
                key={i}
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: color }}
              />
            ))}
          </div>
          <span className="text-sm font-medium">{item.label}</span>
          {currentTheme === item.name && (
            <span className="absolute -top-1 -right-1 w-3 h-3 bg-primary rounded-full" />
          )}
        </button>
      ))}
    </div>
  );
} 