import { NextResponse } from 'next/server';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    
    if (!code) {
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_BASE_URL}/profile?error=no_code`);
    }

    // <PERSON><PERSON><PERSON>, Discord verilerini almak için
    const redirectUrl = `${process.env.NEXT_PUBLIC_BASE_URL}/profile?discord_code=${code}`;
    return NextResponse.redirect(redirectUrl);

  } catch (error: any) {
    console.error('Discord connection error:', error);
    return NextResponse.redirect(
      `${process.env.NEXT_PUBLIC_BASE_URL}/profile?error=connection_failed&message=${encodeURIComponent(error.message)}`
    );
  }
} 