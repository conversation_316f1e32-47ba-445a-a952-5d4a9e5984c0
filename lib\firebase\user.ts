import { db } from '../firebase';
import { doc, getDoc, collection, query, where, getDocs, limit, orderBy, serverTimestamp, runTransaction } from 'firebase/firestore';

export interface Group {
  id: string;
  name: string;
}

interface BaseUser {
  id: string;
  username: string;
  displayName: string;
  email: string | null;
  photoURL: string | null;
  bio: string | null;
}

export interface User extends BaseUser {
  groups: string[];
}

export interface UserWithGroups extends BaseUser {
  groups: Group[];
}

interface UserProfile {
  id: string;
  username: string;
  displayName: string;
  photoURL: string | null;
  isPrivate: boolean;
  linkedAccounts: Record<string, any>;
  bio?: string | null;
  groups: Array<{
    id: string;
    name: string;
  }>;
}

export interface PublicUser {
  createdAt: any;
  uid: string;
  username: string;
  nickname?: string;
  photoURL?: string;
  isPublic: boolean;
}

export async function getUserProfileByUsername(username: string): Promise<UserProfile | null> {
  try {
    const usersRef = collection(db, 'users');
    const q = query(usersRef, where('username', '==', username));
    const snapshot = await getDocs(q);

    if (snapshot.empty) {
      return null;
    }

    const userData = snapshot.docs[0].data();
    
    // Grup bilgilerini çekelim
    const groupsData = await Promise.all(
      (userData.groups || []).map(async (groupId: string) => {
        const groupRef = doc(db, 'groups', groupId);
        const groupSnap = await getDoc(groupRef);
        if (groupSnap.exists()) {
          const groupData = groupSnap.data();
          return {
            id: groupId,
            name: groupData.name
          };
        }
        return null;
      })
    );

    const userProfile: UserProfile = {
      id: snapshot.docs[0].id,
      username: userData.username,
      displayName: userData.nickname || userData.username,
      photoURL: userData.photoURL || null,
      isPrivate: Boolean(!userData.isPublic),
      linkedAccounts: userData.linkedAccounts || {},
      bio: userData.bio || null,
      groups: groupsData.filter((group): group is Group => group !== null)
    };

    return userProfile;
  } catch (error) {
    console.error('Error fetching user profile:', error);
    return null;
  }
}

export async function getUserProfile(userId: string): Promise<User | null> {
  const userRef = doc(db, 'users', userId);
  const userSnap = await getDoc(userRef);

  if (userSnap.exists()) {
    const userData = userSnap.data();

    return {
      id: userSnap.id,
      username: userData.username,
      displayName: userData.nickname || 'Anonymous User',
      email: userData.email || null,
      photoURL: userData.photoURL || null,
      bio: userData.bio || null,
      groups: Array.isArray(userData.groups) ? userData.groups : [],
    };
  } else {
    return null;
  }
}

export async function getUserProfileWithGroups(
  userId: string
): Promise<UserWithGroups | null> {
  const userRef = doc(db, 'users', userId);
  const userSnap = await getDoc(userRef);

  if (userSnap.exists()) {
    const userData = userSnap.data();

    // Kullanıcıdan gelen grup ID'leri
    const groupIds = Array.isArray(userData.groups) ? userData.groups : [];

    // Grupların bilgilerini çekme
    const groups = await Promise.all(
      groupIds.map(async (groupId) => {
        const groupRef = doc(db, 'groups', groupId);
        const groupSnap = await getDoc(groupRef);
        if (groupSnap.exists()) {
          return { id: groupSnap.id, ...groupSnap.data() };
        } else {
          return null;
        }
      })
    );

    // Geçerli grup bilgilerini filtreleme
    const validGroups = groups.filter((group) => group !== null);

    return {
      id: userSnap.id,
      username: userData.username,
      displayName: userData.nickname || 'Anonymous User',
      email: userData.email || null,
      photoURL: userData.photoURL || null,
      bio: userData.bio || null,
      groups: validGroups as { id: string; name: string }[],
    };
  } else {
    return null;
  }
}

export async function getUsers() {
  const usersQuery = query(
    collection(db, 'users'),
    where('isPublic', '==', true)
  )
  
  const usersSnapshot = await getDocs(usersQuery)
  return usersSnapshot.docs.map(doc => ({
    id: doc.id,
    username: doc.data().username,
    ...doc.data(),
    updatedAt: doc.data().updatedAt?.toDate?.() || new Date()
  }))
}

export async function getPublicUsers(): Promise<PublicUser[]> {
  try {
    const usersQuery = query(
      collection(db, "users"),
      where("isPublic", "==", true)
    );
    
    const querySnapshot = await getDocs(usersQuery);
    return querySnapshot.docs.map(doc => ({
      uid: doc.id,
      ...doc.data(),
    } as PublicUser));
  } catch (error) {
    console.error("Error fetching public users:", error);
    return [];
  }
}