"use client";

import { useStats } from '@/lib/hooks/useStats';
import { Card } from '@/components/ui/card';
import { Users, Gamepad2, Trophy, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';

export function StatsSection() {
  const { activeUsers, activeGroups, loading, error } = useStats();

  const stats = [
    {
      icon: Users,
      value: loading ? '-' : `${activeUsers.toLocaleString()}+`,
      label: 'Aktif <PERSON>yuncu',
      description: 'Her gün büyüyen topluluğumuz'
    },
    {
      icon: Gamepad2,
      value: loading ? '-' : `${activeGroups.toLocaleString()}+`,
      label: 'Aktif Grup',
      description: 'Farklı oyun kategorilerinde'
    },
    {
      icon: Trophy,
      value: '50,000+',
      label: 'Başarılı Eşleşme',
      description: 'Mutlu oyuncular'
    }
  ];

  if (error) {
    return (
      <section className="py-24 px-4">
        <div className="container mx-auto max-w-6xl text-center">
          <AlertTriangle className="w-12 h-12 mx-auto mb-4 text-destructive" />
          <h2 className="text-2xl font-bold mb-4">İstatistikler Yüklenemedi</h2>
          <p className="text-muted-foreground mb-6">
            {error.message || 'Veriler yüklenirken bir hata oluştu'}
          </p>
          <Button variant="outline" onClick={() => window.location.reload()}>
            Tekrar Dene
          </Button>
        </div>
      </section>
    );
  }

  return (
    <section className="py-24 px-4">
      <div className="container mx-auto max-w-6xl">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold mb-4">Rakamlarla Biz</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Türkiye'nin en büyük oyuncu topluluğuna katılın
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            const isLoading = loading && index < 2; // Only show loading for dynamic stats

            return (
              <Card key={index} className="p-8 text-center">
                <Icon className={`w-12 h-12 mx-auto mb-4 text-primary ${isLoading ? 'animate-pulse' : ''}`} />
                <div className="space-y-2">
                  <h3 className={`text-4xl font-bold ${isLoading ? 'animate-pulse bg-muted rounded' : ''}`}>
                    {error && index < 2 ? '---' : stat.value}
                  </h3>
                  <p className="text-lg font-medium">{stat.label}</p>
                  <p className="text-sm text-muted-foreground">{stat.description}</p>
                </div>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
}