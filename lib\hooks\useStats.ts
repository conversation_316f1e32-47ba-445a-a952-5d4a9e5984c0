"use client";

import { useState, useEffect } from 'react';
import { getStats } from '@/lib/services/stats';

export function useStats() {
  const [stats, setStats] = useState({
    activeUsers: 0,
    activeGroups: 0,
    loading: true,
    error: null as Error | null,
  });

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const data = await getStats();
        setStats(prev => ({
          ...prev,
          activeUsers: data.activeUsers,
          activeGroups: data.activeGroups,
          loading: false,
        }));
      } catch (error) {
        setStats(prev => ({
          ...prev,
          error: error as Error,
          loading: false,
        }));
      }
    };

    fetchStats();

    // Set up periodic refresh
    const intervalId = setInterval(fetchStats, 3 * 60 * 1000); // 3 minutes

    return () => clearInterval(intervalId);
  }, []);

  return stats;
}