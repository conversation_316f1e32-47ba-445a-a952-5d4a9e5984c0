import { Button } from "./button";
import { Upload } from "lucide-react";

interface UploadButtonProps {
  onUpload: (file: File) => void;
  accept?: string;
  className?: string;
  disabled?: boolean;
}

export function UploadButton({ onUpload, accept, className, disabled }: UploadButtonProps) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      onUpload(file);
    }
  };

  return (
    <Button
      variant="outline"
      size="sm"
      className={className}
      onClick={() => document.getElementById('file-upload')?.click()}
      disabled={disabled}
    >
      <Upload className="h-4 w-4 mr-2" />
      {disabled ? 'Yükleniyor...' : 'Görse<PERSON> Yükle'}
      <input
        id="file-upload"
        type="file"
        accept={accept}
        onChange={handleChange}
        className="hidden"
        disabled={disabled}
      />
    </Button>
  );
} 