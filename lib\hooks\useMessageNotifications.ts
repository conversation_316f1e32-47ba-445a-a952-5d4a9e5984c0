import { useEffect, useRef } from 'react';
import { doc, onSnapshot } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { useToast } from '@/components/ui/use-toast';

export function useMessageNotifications(userId: string | undefined) {
  const { toast } = useToast();
  const audioContext = useRef<AudioContext | null>(null);
  const audioBuffer = useRef<AudioBuffer | null>(null);
  const isAudioLoaded = useRef(false);

  useEffect(() => {
    if (!userId || typeof window === 'undefined') return;

    const loadAudio = async () => {
      try {
        // Only initialize audio context on first load
        if (!audioContext.current) {
          audioContext.current = new (window.AudioContext || (window as any).webkitAudioContext)();
        }

        // Only load audio once
        if (!isAudioLoaded.current) {
          const response = await fetch('/sounds/notification.mp3');
          if (!response.ok) {
            throw new Error('Failed to load audio file');
          }
          
          const arrayBuffer = await response.arrayBuffer();
          audioBuffer.current = await audioContext.current.decodeAudioData(arrayBuffer);
          isAudioLoaded.current = true;
        }
      } catch (error) {
        console.error('Error initializing audio:', error);
      }
    };

    loadAudio();

    const unsubscribe = onSnapshot(
      doc(db, 'users', userId),
      (doc) => {
        const userData = doc.data();
        if (userData?.unreadMessages > 0) {
          const hasInteracted = document.documentElement.hasAttribute('data-user-interacted');
          
          if (hasInteracted && audioContext.current && audioBuffer.current) {
            try {
              // Resume audio context if it's suspended
              if (audioContext.current.state === 'suspended') {
                audioContext.current.resume();
              }

              const source = audioContext.current.createBufferSource();
              source.buffer = audioBuffer.current;
              source.connect(audioContext.current.destination);
              source.start(0);
            } catch (error) {
              console.error('Error playing notification sound:', error);
            }
          }

          toast({
            title: 'Yeni Mesaj',
            description: 'Yeni bir mesajınız var!',
          });
        }
      },
      (error) => {
        console.error('Error listening to notifications:', error);
      }
    );

    return () => {
      unsubscribe();
      // Don't close the audio context as it might be needed again
    };
  }, [userId, toast]);
}
