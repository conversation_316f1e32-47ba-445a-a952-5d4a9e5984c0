"use client";

import { Navbar } from '@/components/navbar';
import { Toaster } from '@/components/ui/toaster';
import { UserInteractionTracker } from '@/components/user-interaction-tracker';
import { AuthChecker } from '@/components/auth-checker';
import { Footer } from '@/components/home/<USER>';
import { QueryProvider } from "@/lib/providers/query-provider";
import { initializePresence } from '@/lib/firebase/presence';
import { useEffect } from 'react';
import { DocumentData } from 'firebase/firestore';

interface LayoutProviderProps {
  settings: DocumentData | null; // Allow null if necessary
  children: React.ReactNode;
}

export function LayoutProvider({ settings, children }: LayoutProviderProps) {
  useEffect(() => {
    initializePresence();
  }, []);

  return (
    <QueryProvider>
      <div className="flex flex-col min-h-screen">
        <AuthChecker />
        <UserInteractionTracker />
        <Navbar />
        <main className="flex-1">
          {children}
        </main>
        <Toaster />
        <Footer />
      </div>
    </QueryProvider>
  );
} 