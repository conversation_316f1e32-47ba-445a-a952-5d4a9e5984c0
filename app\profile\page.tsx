"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/lib/hooks/useAuth";
import { updateProfile } from "firebase/auth";
import { doc, getDoc, updateDoc } from "firebase/firestore";
import { ref, uploadBytes, getDownloadURL } from "firebase/storage";
import { auth, db, storage } from "@/lib/firebase";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { useToast } from "@/components/ui/use-toast";
import { Pencil, Upload, Loader2, Trophy, UserCircle, Shield, Lock, Bell, Link as LinkIcon, Paintbrush } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { <PERSON>aGoo<PERSON>, <PERSON>a<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-icons/fa";
import { signInWithPopup, OAuthProvider } from 'firebase/auth';
import { ensureUserDocument } from "@/lib/firebase";
import { User } from "@/lib/types/firebase";

type LinkedAccountData = {
  google: {
    linked: boolean;
    data: { email?: string } | null;
    isPublic?: boolean;
  };
  twitch: {
    linked: boolean;
    data: { username?: string } | null;
    isPublic?: boolean;
  };
  discord: {
    linked: boolean;
    data: { username?: string } | null;
    isPublic?: boolean;
  };
  twitter: {
    linked: boolean;
    data: { username?: string } | null;
    isPublic?: boolean;
  };
};

export default function ProfilePage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const { toast } = useToast();
  const [isEditing, setIsEditing] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [nickname, setNickname] = useState('');
  const [isPublic, setIsPublic] = useState(true);
  const [linkedAccounts, setLinkedAccounts] = useState<LinkedAccountData>({
    google: { linked: false, data: null },
    twitch: { linked: false, data: null },
    discord: { linked: false, data: null },
    twitter: { linked: false, data: null },
  });
  const [activeTab, setActiveTab] = useState(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('profileActiveTab') || 'profile';
    }
    return 'profile';
  });

  useEffect(() => {
    if (user) {
      setNickname(user.displayName || user.email?.split('@')[0] || '');

      const fetchUserData = async () => {
        const userDoc = await getDoc(doc(db, "users", user.uid));
        if (userDoc.exists()) {
          const userData = userDoc.data();
          setIsPublic(userData.isPublic ?? true);
          const userLinkedAccounts = userData.linkedAccounts || {};
          setLinkedAccounts({
            google: userLinkedAccounts.google || { linked: false, data: null },
            twitch: userLinkedAccounts.twitch || { linked: false, data: null },
            discord: userLinkedAccounts.discord || { linked: false, data: null },
            twitter: userLinkedAccounts.twitter || { linked: false, data: null },
          });
        }
      };

      fetchUserData();
    }
  }, [user]);

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const error = params.get('error');
    const success = params.get('success');
    const message = params.get('message');
    const twitch_code = params.get('twitch_code');
    const discord_code = params.get('discord_code');

    if (error || success || message || twitch_code || discord_code) {
      setActiveTab('connections');
    }

    if (error) {
      let errorMessage = "Bir hata oluştu.";
      switch (error) {
        case 'no_code':
          errorMessage = "Twitch yetkilendirme kodu alınamadı.";
          break;
        case 'not_authenticated':
          errorMessage = "Oturum açmanız gerekiyor.";
          break;
        case 'connection_failed':
          errorMessage = message || "Twitch bağlantısı başarısız oldu.";
          break;
        case 'access_denied':
          errorMessage = "Twitch erişimi reddedildi.";
          break;
      }
      toast({
        title: "Hata",
        description: errorMessage,
        variant: "destructive",
      });
    }

    if (success === 'twitch_connected') {
      toast({
        title: "Başarılı",
        description: "Twitch hesabınız başarıyla bağlandı.",
      });
    }

    if (error || success) {
      window.history.replaceState({}, '', '/profile');
    }
  }, [toast]);

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const twitch_code = params.get('twitch_code');

    async function connectTwitch(code: string) {
      try {
        // Auth kontrolü yap
        if (!user || loading) {
          // console.log('Waiting for auth...', { user, loading });
          return; // Henüz auth hazır değil, işlemi yapma
        }

        // Kullanıcı dökümanını kontrol et/oluştur
        await ensureUserDocument(user);

        // URL'den parametreyi hemen temizle
        window.history.replaceState({}, '', '/profile');

        // Token al
        const tokenResponse = await fetch('/api/auth/twitch/token', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ code }),
        });

        if (!tokenResponse.ok) {
          const error = await tokenResponse.json();
          throw new Error(error.error || 'Failed to fetch Twitch token');
        }
        const tokenData = await tokenResponse.json();

        // Twitch kullanıcı bilgilerini al
        const userResponse = await fetch('https://api.twitch.tv/helix/users', {
          headers: {
            Authorization: `Bearer ${tokenData.access_token}`,
            'Client-Id': process.env.NEXT_PUBLIC_TWITCH_CLIENT_ID!,
          },
        });

        if (!userResponse.ok) throw new Error('Failed to fetch Twitch user data');
        const userData = await userResponse.json();
        const twitchUser = userData.data[0];

        // Firestore güncelle
        const userRef = doc(db, 'users', user.uid);
        const userDoc = await getDoc(userRef);

        if (!userDoc.exists()) {
          throw new Error('User document not found');
        }

        const currentData = userDoc.data();
        const twitchData = {
          linked: true,
          data: {
            id: twitchUser.id,
            username: twitchUser.login,
            email: twitchUser.email,
            profileImageUrl: twitchUser.profile_image_url,
          },
          accessToken: tokenData.access_token,
          refreshToken: tokenData.refresh_token,
          expiresAt: Date.now() + tokenData.expires_in * 1000,
        };

        // Profil resmini güncelle
        if (user.photoURL?.includes('dicebear.com')) {
          await updateProfile(auth.currentUser!, { 
            photoURL: twitchUser.profile_image_url 
          });
          await updateDoc(userRef, { 
            photoURL: twitchUser.profile_image_url 
          });
        }

        await updateDoc(userRef, {
          linkedAccounts: {
            ...currentData.linkedAccounts,
            twitch: twitchData
          }
        });

        // State'i güncelle
        setLinkedAccounts(prev => ({
          ...prev,
          twitch: twitchData
        }));

        toast({
          title: "Başarılı",
          description: "Twitch hesabınız başarıyla bağlandı.",
        });

      } catch (error: any) {
        console.error('Twitch connection error:', error);
        toast({
          title: "Hata",
          description: error.message || "Twitch bağlantısı sırasında bir hata oluştu.",
          variant: "destructive",
        });
      }
    }

    if (twitch_code && user && !loading) {
      connectTwitch(twitch_code);
    }
  }, [user, loading, toast]);

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const discord_code = params.get('discord_code');

    async function connectDiscord(code: string) {
      try {
        // Auth kontrolü yap
        if (!user || loading) {
          // console.log('Waiting for auth...', { user, loading });
          return; // Henüz auth hazır değil, işlemi yapma
        }

        // Kullanıcı dökümanını kontrol et/oluştur
        await ensureUserDocument(user);

        // URL'den parametreyi hemen temizle
        window.history.replaceState({}, '', '/profile');

        // Token al
        const tokenResponse = await fetch('/api/auth/discord/token', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ code }),
        });

        if (!tokenResponse.ok) {
          const error = await tokenResponse.json();
          throw new Error(error.error || 'Failed to fetch Discord token');
        }
        const tokenData = await tokenResponse.json();

        // Discord kullanıcı bilgilerini al
        const userResponse = await fetch('https://discord.com/api/users/@me', {
          headers: {
            Authorization: `Bearer ${tokenData.access_token}`,
          },
        });

        if (!userResponse.ok) throw new Error('Failed to fetch Discord user data');
        const discordUser = await userResponse.json();

        // Firestore güncelle
        const userRef = doc(db, 'users', user.uid);
        const userDoc = await getDoc(userRef);

        if (!userDoc.exists()) {
          throw new Error('User document not found');
        }

        const currentData = userDoc.data();
        const discordAvatarUrl = discordUser.avatar ? 
          `https://cdn.discordapp.com/avatars/${discordUser.id}/${discordUser.avatar}.png` : 
          null;

        const discordData = {
          linked: true,
          data: {
            id: discordUser.id,
            username: discordUser.username,
            email: discordUser.email,
            avatar: discordAvatarUrl,
          },
          accessToken: tokenData.access_token,
          refreshToken: tokenData.refresh_token,
          expiresAt: Date.now() + (tokenData.expires_in * 1000),
        };

        // Profil resmini güncelle
        if (discordAvatarUrl && user.photoURL?.includes('dicebear.com')) {
          await updateProfile(auth.currentUser!, { 
            photoURL: discordAvatarUrl 
          });
          await updateDoc(userRef, { 
            photoURL: discordAvatarUrl 
          });
        }

        await updateDoc(userRef, {
          linkedAccounts: {
            ...currentData.linkedAccounts,
            discord: discordData
          }
        });

        // State'i güncelle
        setLinkedAccounts(prev => ({
          ...prev,
          discord: discordData
        }));

        toast({
          title: "Başarılı",
          description: "Discord hesabınız başarıyla bağlandı.",
        });

      } catch (error: any) {
        console.error('Discord connection error:', error);
        toast({
          title: "Hata",
          description: error.message || "Discord bağlantısı sırasında bir hata oluştu.",
          variant: "destructive",
        });
      }
    }

    if (discord_code && user && !loading) {
      connectDiscord(discord_code);
    }
  }, [user, loading, toast]);

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const code = params.get('code');
    const state = params.get('state');

    async function connectTwitter(code: string, state: string) {
      try {
        if (!user || loading) {
          // console.log('Waiting for auth...', { user, loading });
          return;
        }

        await ensureUserDocument(user);
        window.history.replaceState({}, '', '/profile');

        // Twitter token'ı al
        const tokenResponse = await fetch('/api/auth/twitter/token', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ code, state }),
        });

        // console.log('Token response:', await tokenResponse.clone().json()); // Debug için

        if (!tokenResponse.ok) {
          const error = await tokenResponse.json();
          throw new Error(error.error || 'Failed to fetch Twitter token');
        }
        const tokenData = await tokenResponse.json();

        // Firestore güncelle
        const userRef = doc(db, 'users', user.uid);
        const userDoc = await getDoc(userRef);

        if (!userDoc.exists()) {
          throw new Error('User document not found');
        }

        const currentData = userDoc.data();
        const twitterData = {
          linked: true,
          data: {
            id: tokenData.user_id,
            username: tokenData.screen_name,
            profileImageUrl: tokenData.profile_image_url,
          },
          accessToken: tokenData.access_token,
        };

        await updateDoc(userRef, {
          linkedAccounts: {
            ...currentData.linkedAccounts,
            twitter: twitterData
          }
        });

        // State'i güncelle
        setLinkedAccounts(prev => ({
          ...prev,
          twitter: twitterData
        }));

        toast({
          title: "Başarılı",
          description: "Twitter hesabınız başarıyla bağlandı.",
        });

      } catch (error: any) {
        console.error('Twitter connection error:', error);
        toast({
          title: "Hata",
          description: error.message || "Twitter bağlantısı sırasında bir hata oluştu.",
          variant: "destructive",
        });
      }
    }

    // URL'de code ve state varsa Twitter callback'i olarak işle
    if (code && state && user && !loading) {
      // console.log('Twitter callback detected:', { code, state }); // Debug için
      connectTwitter(code, state);
    }
  }, [user, loading, toast]);

  useEffect(() => {
    if (!loading && !user) {
      router.replace("/auth/sign-in");
    }
  }, [user, loading, router]);

  useEffect(() => {
    if (user) {
      // Session cookie'sini oluştur
      const setCookie = () => {
        document.cookie = `session=${user.uid}; path=/`;
      };
      setCookie();
    }
  }, [user]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  const handlePhotoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Dosya tipini kontrol et
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Hata",
        description: "Lütfen sadece resim dosyası yükleyin.",
        variant: "destructive",
      });
      return;
    }

    try {
      setUploading(true);

      // FormData oluştur
      const formData = new FormData();
      formData.append('file', file);
      formData.append('userId', user.uid);

      // Dosyayı API'ye gönder
      const response = await fetch('/api/upload/profile-photo', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Dosya yükleme hatası');
      }

      const data = await response.json();
      const photoURL = data.url; // /public/uploads/profile-photos/user_id.jpg gibi

      // Firestore ve Auth profilini güncelle
      await updateProfile(auth.currentUser!, { photoURL });
      await updateDoc(doc(db, "users", user.uid), { photoURL });

      toast({
        title: "Profil fotoğrafı güncellendi",
        description: "Fotoğrafınız başarıyla değiştirildi.",
      });
    } catch (error) {
      console.error("Error uploading photo:", error);
      toast({
        title: "Hata",
        description: "Fotoğraf yüklenirken bir hata oluştu.",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  const handleNicknameUpdate = async () => {
    if (!nickname.trim()) return;

    try {
      await updateProfile(auth.currentUser!, { displayName: nickname });
      await updateDoc(doc(db, "users", user.uid), { nickname });

      setIsEditing(false);
      toast({
        title: "Profil güncellendi",
        description: "Takma adınız başarıyla değiştirildi.",
      });
    } catch (error) {
      console.error("Error updating nickname:", error);
      toast({
        title: "Hata",
        description: "Takma ad güncellenirken bir hata oluştu.",
        variant: "destructive",
      });
    }
  };

  const handlePrivacyToggle = async () => {
    try {
      const newIsPublic = !isPublic;
      await updateDoc(doc(db, "users", user.uid), {
        isPublic: newIsPublic,
      });
      setIsPublic(newIsPublic);
      toast({
        title: "Gizlilik ayarı güncellendi",
        description: newIsPublic
          ? "Profiliniz artık herkese açık."
          : "Profiliniz artık gizli.",
      });
    } catch (error) {
      console.error("Error updating privacy settings:", error);
      toast({
        title: "Hata",
        description: "Gizlilik ayarları güncellenirken bir hata oluştu.",
        variant: "destructive",
      });
    }
  };

  const handleConnect = async (platform: 'google' | 'twitch' | 'discord' | 'twitter') => {
    try {
      // console.log(`${platform} bağlantısı başlatılıyor...`);

      if (platform === 'twitch') {
        window.location.href = '/api/auth/twitch';
        return;
      }
      if (platform === 'discord') {
        window.location.href = '/api/auth/discord';
        return;
      }
      if (platform === 'twitter') {
        window.location.href = '/api/auth/twitter';
        return;
      }

      toast({
        title: "Bağlantı İsteği",
        description: `${platform.charAt(0).toUpperCase() + platform.slice(1)} hesabı bağlama işlemi başlatılıyor...`,
      });
    } catch (error) {
      console.error(`Error connecting ${platform}:`, error);
      toast({
        title: "Hata",
        description: "Hesap bağlama işlemi sırasında bir hata oluştu.",
        variant: "destructive",
      });
    }
  };

  const handleDisconnect = async (platform: 'google' | 'twitch' | 'discord' | 'twitter') => {
    try {
      const userRef = doc(db, 'users', user.uid);
      const userDoc = await getDoc(userRef);

      if (!userDoc.exists()) {
        throw new Error('User document not found');
      }

      const currentData = userDoc.data();
      const updatedLinkedAccounts = { ...currentData.linkedAccounts };
      updatedLinkedAccounts[platform] = { linked: false, data: null };

      await updateDoc(userRef, {
        linkedAccounts: updatedLinkedAccounts
      });

      setLinkedAccounts(prev => ({
        ...prev,
        [platform]: { linked: false, data: null }
      }));

      toast({
        title: "Başarılı",
        description: `${platform.charAt(0).toUpperCase() + platform.slice(1)} hesabı bağlantısı kesildi.`,
      });
    } catch (error) {
      console.error(`Error disconnecting ${platform}:`, error);
      toast({
        title: "Hata",
        description: "Hesap bağlantısı kesilirken bir hata oluştu.",
        variant: "destructive",
      });
    }
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    localStorage.setItem('profileActiveTab', tab);
  };

  const handleVisibilityToggle = async (platform: 'google' | 'twitch' | 'discord' | 'twitter') => {
    try {
      const userRef = doc(db, 'users', user.uid);
      const userDoc = await getDoc(userRef);

      if (!userDoc.exists()) {
        throw new Error('User document not found');
      }

      const currentData = userDoc.data();
      const updatedLinkedAccounts = { ...currentData.linkedAccounts };
      
      // Toggle isPublic değeri
      updatedLinkedAccounts[platform] = {
        ...updatedLinkedAccounts[platform],
        isPublic: !updatedLinkedAccounts[platform]?.isPublic
      };

      await updateDoc(userRef, {
        linkedAccounts: updatedLinkedAccounts
      });

      setLinkedAccounts(prev => ({
        ...prev,
        [platform]: {
          ...prev[platform],
          isPublic: !prev[platform]?.isPublic
        }
      }));

      toast({
        title: "Başarılı",
        description: `${platform.charAt(0).toUpperCase() + platform.slice(1)} hesabı görünürlüğü güncellendi.`,
      });
    } catch (error) {
      console.error(`Error updating ${platform} visibility:`, error);
      toast({
        title: "Hata",
        description: "Görünürlük ayarı güncellenirken bir hata oluştu.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="flex justify-center w-full">
      <div className="w-full max-w-6xl mx-auto py-10 px-4 sm:px-6 lg:px-8">
        {/* Üst Profil Kartı */}
        <Card className="mb-8">
          <div className="flex flex-col sm:flex-row items-center p-6 gap-6">
            <div className="flex items-center gap-6">
              <div className="relative">
                <Avatar className="h-24 w-24">
                  <AvatarImage 
                    src={user.photoURL || `https://api.dicebear.com/7.x/avataaars/svg?seed=${user.uid}`} 
                  />
                  <AvatarFallback>
                    {nickname?.charAt(0) || user.email?.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <Label 
                  htmlFor="photo-upload" 
                  className="absolute bottom-0 right-0 cursor-pointer"
                >
                  <div className="bg-primary text-primary-foreground p-2 rounded-full hover:opacity-90">
                    {uploading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Upload className="h-4 w-4" />
                    )}
                  </div>
                </Label>
                <Input
                  type="file"
                  accept="image/*"
                  className="hidden"
                  id="photo-upload"
                  onChange={handlePhotoUpload}
                  disabled={uploading}
                />
              </div>
              <div>
                <h2 className="text-xl font-bold">{nickname}</h2>
                <p className="text-muted-foreground">Üye</p>
                <div className="mt-2">
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary/20 text-primary">
                    <Trophy className="w-4 h-4 mr-1" />
                    Seviye 1
                  </span>
                </div>
              </div>
            </div>
          </div>
        </Card>

        {/* İçerik Alanı */}
        <div className="space-y-8">
          {/* Profil Bilgileri */}
          <Card className="p-6">
            <h3 className="text-lg font-medium mb-6">Profil Bilgileri</h3>
            <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
              <div className="sm:col-span-3 space-y-2">
                <Label>Kullanıcı Adı (ID)</Label>
                <Input value={user.username} disabled />
              </div>
              <div className="sm:col-span-3 space-y-2">
                <Label>E-posta</Label>
                <Input value={user.email || ""} disabled />
              </div>
              <div className="sm:col-span-6 space-y-2">
                <Label>Takma Ad</Label>
                {isEditing ? (
                  <div className="flex gap-2">
                    <Input
                      value={nickname}
                      onChange={(e) => setNickname(e.target.value)}
                      placeholder="Takma adınızı girin"
                    />
                    <Button onClick={handleNicknameUpdate}>Kaydet</Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setIsEditing(false);
                        setNickname(user.displayName || "");
                      }}
                    >
                      İptal
                    </Button>
                  </div>
                ) : (
                  <div className="flex gap-2">
                    <Input value={nickname} disabled />
                    <Button variant="outline" onClick={() => setIsEditing(true)}>
                      <Pencil className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </Card>

          {/* Bağlı Hesaplar */}
          <Card className="p-6">
            <h3 className="text-lg font-medium mb-6">Bağlı Hesaplar</h3>
            <div className="space-y-4">
              {/* Google */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="bg-background p-2 rounded-full">
                    <FaGoogle className="w-5 h-5" />
                  </div>
                  <div>
                    <h4 className="font-medium">Google</h4>
                    {linkedAccounts?.google?.linked ? (
                      <p className="text-sm text-muted-foreground">
                        {linkedAccounts.google.data?.email}
                      </p>
                    ) : (
                      <p className="text-sm text-muted-foreground">Bağlı Değil</p>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-4 flex-col-reverse md:flex-row">
                  {linkedAccounts?.google?.linked && (
                    <div className="flex items-center gap-2">
                      <Label htmlFor="google-visibility" className="text-sm">
                        Herkese Açık
                      </Label>
                      <Switch
                        id="google-visibility"
                        checked={linkedAccounts.google.isPublic}
                        onCheckedChange={() => handleVisibilityToggle('google')}
                      />
                    </div>
                  )}
                  {linkedAccounts?.google?.linked ? (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDisconnect('google')}
                    >
                      Bağlantıyı Kes
                    </Button>
                  ) : (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleConnect('google')}
                    >
                      Hesabını Bağla
                    </Button>
                  )}
                </div>
              </div>

              {/* Twitch */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="bg-background p-2 rounded-full">
                    <FaTwitch className="w-5 h-5" />
                  </div>
                  <div>
                    <h4 className="font-medium">Twitch</h4>
                    {linkedAccounts?.twitch?.linked ? (
                      <p className="text-sm text-muted-foreground">
                        {linkedAccounts.twitch.data?.username}
                      </p>
                    ) : (
                      <p className="text-sm text-muted-foreground">Bağlı Değil</p>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-4 flex-col-reverse md:flex-row">
                  {linkedAccounts?.twitch?.linked && (
                    <div className="flex items-center gap-2">
                      <Label htmlFor="twitch-visibility" className="text-sm">
                        Herkese Açık
                      </Label>
                      <Switch
                        id="twitch-visibility"
                        checked={linkedAccounts.twitch.isPublic}
                        onCheckedChange={() => handleVisibilityToggle('twitch')}
                      />
                    </div>
                  )}
                  {linkedAccounts?.twitch?.linked ? (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDisconnect('twitch')}
                    >
                      Bağlantıyı Kes
                    </Button>
                  ) : (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleConnect('twitch')}
                    >
                      Hesabını Bağla
                    </Button>
                  )}
                </div>
              </div>

              {/* Discord */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="bg-background p-2 rounded-full">
                    <FaDiscord className="w-5 h-5" />
                  </div>
                  <div>
                    <h4 className="font-medium">Discord</h4>
                    {linkedAccounts?.discord?.linked ? (
                      <p className="text-sm text-muted-foreground">
                        {linkedAccounts.discord.data?.username}
                      </p>
                    ) : (
                      <p className="text-sm text-muted-foreground">Bağlı Değil</p>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-4 flex-col-reverse md:flex-row">
                  {linkedAccounts?.discord?.linked && (
                    <div className="flex items-center gap-2">
                      <Label htmlFor="discord-visibility" className="text-sm">
                        Herkese Açık
                      </Label>
                      <Switch
                        id="discord-visibility"
                        checked={linkedAccounts.discord.isPublic}
                        onCheckedChange={() => handleVisibilityToggle('discord')}
                      />
                    </div>
                  )}
                  {linkedAccounts?.discord?.linked ? (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDisconnect('discord')}
                    >
                      Bağlantıyı Kes
                    </Button>
                  ) : (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleConnect('discord')}
                    >
                      Hesabını Bağla
                    </Button>
                  )}
                </div>
              </div>

              {/* Twitter */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="bg-background p-2 rounded-full">
                    <FaTwitter className="w-5 h-5" />
                  </div>
                  <div>
                    <h4 className="font-medium">Twitter</h4>
                    {linkedAccounts?.twitter?.linked ? (
                      <p className="text-sm text-muted-foreground">
                        {linkedAccounts.twitter.data?.username}
                      </p>
                    ) : (
                      <p className="text-sm text-muted-foreground">Bağlı Değil</p>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-4 flex-col-reverse md:flex-row">
                  {linkedAccounts?.twitter?.linked && (
                    <div className="flex items-center gap-2">
                      <Label htmlFor="twitter-visibility" className="text-sm">
                        Herkese Açık
                      </Label>
                      <Switch
                        id="twitter-visibility"
                        checked={linkedAccounts.twitter.isPublic}
                        onCheckedChange={() => handleVisibilityToggle('twitter')}
                      />
                    </div>
                  )}
                  {linkedAccounts?.twitter?.linked ? (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDisconnect('twitter')}
                    >
                      Bağlantıyı Kes
                    </Button>
                  ) : (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleConnect('twitter')}
                    >
                      Hesabını Bağla
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}