"use client";

import Link from 'next/link';
import { useGroups } from '@/lib/hooks/useGroups';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Users, ArrowRight } from 'lucide-react';
import { Group } from '@/lib/types/firebase';

function GroupsList() {
  const { data: groups, isLoading, error } = useGroups({ limitCount: 4 });

  if (error) {
    return (
      <Card className="p-6 text-center">
        <p className="text-muted-foreground mb-4">
          Gruplar yüklenirken bir hata oluştu.
        </p>
        <Button variant="outline" onClick={() => window.location.reload()}>
          Tekrar Dene
        </Button>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="p-6">
            <div className="h-4 bg-muted rounded w-3/4 mb-4" />
            <div className="h-4 bg-muted rounded w-1/2" />
          </Card>
        ))}
      </div>
    );
  }

  if (!groups || groups.length === 0) {
    return (
      <Card className="p-6 text-center">
        <p className="text-muted-foreground">
          Henüz grup bulunmuyor.
        </p>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {groups.map((group: Group) => (
        <Link key={group.id} href={`/groups/${group.id}`}>
          <Card className="p-6 h-full hover:shadow-lg transition-shadow">
            <h3 className="font-semibold mb-2 line-clamp-1">{group.name}</h3>
            <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
              {group.description}
            </p>
            <div className="flex items-center justify-between mt-auto">
              <Badge variant="secondary" className="flex items-center gap-1">
                <Users className="w-3 h-3" />
                {group.memberCount}
              </Badge>
              <Badge>{group.category}</Badge>
            </div>
          </Card>
        </Link>
      ))}
    </div>
  );
}

export function FeaturedGroups() {
  const { data: groups, isLoading } = useGroups({
    limitCount: 4,
    // Öne çıkan gruplar için filtre ekleyebiliriz
  });

  return (
    <section className="py-24 px-4 bg-muted/50">
      <div className="container mx-auto max-w-6xl">
        <div className="flex justify-between items-center mb-12">
          <div>
            <h2 className="text-3xl font-bold mb-2">Öne Çıkan Gruplar</h2>
            <p className="text-muted-foreground">
              En popüler oyun gruplarını keşfedin
            </p>
          </div>
          <Button variant="outline" asChild>
            <Link href="/groups" className="flex items-center gap-2">
              Tümünü Gör
              <ArrowRight className="w-4 h-4" />
            </Link>
          </Button>
        </div>

        {/* İçerik kısmı */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <div className="h-4 bg-muted rounded w-3/4 mb-4" />
                <div className="h-4 bg-muted rounded w-1/2" />
              </Card>
            ))}
          </div>
        ) : !groups || groups.length === 0 ? (
          <Card className="p-6 text-center">
            <p className="text-muted-foreground">
              Henüz grup bulunmuyor.
            </p>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {groups.map((group: Group) => (
              <Link key={group.id} href={`/groups/${group.id}`}>
                <Card className="p-6 h-full hover:shadow-lg transition-shadow">
                  <h3 className="font-semibold mb-2 line-clamp-1">{group.name}</h3>
                  <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
                    {group.description}
                  </p>
                  <div className="flex items-center justify-between mt-auto">
                    <Badge variant="secondary" className="flex items-center gap-1">
                      <Users className="w-3 h-3" />
                      {group.memberCount}
                    </Badge>
                    <Badge>{group.category}</Badge>
                  </div>
                </Card>
              </Link>
            ))}
          </div>
        )}
      </div>
    </section>
  );
}