'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { AlertTriangle } from 'lucide-react';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    console.error(error);
  }, [error]);

  return (
    <div className="min-h-[80vh] flex items-center justify-center">
      <div className="container max-w-md text-center">
        <AlertTriangle className="w-16 h-16 mx-auto mb-8 text-destructive" />
        <h1 className="text-2xl font-bold mb-4">Bir Hata Oluştu</h1>
        <p className="text-muted-foreground mb-8">
          Üzgünüz, bir şeyler yanlış gitti. Lütfen tekrar deneyin veya ana sayfaya dönün.
        </p>
        <div className="flex gap-4 justify-center">
          <Button onClick={reset} variant="outline">
            <PERSON><PERSON><PERSON>e
          </Button>
          <Button asChild>
            <Link href="/">Ana Sayfaya Dön</Link>
          </Button>
        </div>
      </div>
    </div>
  );
}