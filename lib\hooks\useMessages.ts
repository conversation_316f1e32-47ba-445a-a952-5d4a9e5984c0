"use client";

import { useState, useEffect, useCallback } from 'react';
import { 
  collection, 
  increment,
  query, 
  where, 
  orderBy, 
  onSnapshot,
  addDoc,
  updateDoc,
  doc,
  serverTimestamp,
  getDocs,
  Timestamp,
  writeBatch
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { Message, Chat } from '@/lib/types/firebase';

export function useMessages(userId: string) {
  const [chats, setChats] = useState<Chat[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    if (!userId) {
      setChats([]);
      setLoading(false);
      return;
    }

    const chatsQuery = query(
      collection(db, 'chats'),
      where('participants', 'array-contains', userId),
      orderBy('updatedAt', 'desc')
    );

    const unsubscribe = onSnapshot(chatsQuery, 
      async (snapshot) => {
        const chatPromises = snapshot.docs.map(async (chatDoc) => {
          const messagesQuery = query(
            collection(db, 'messages'),
            where('chatId', '==', chatDoc.id),
            orderBy('createdAt', 'asc')
          );
          
          const messagesSnap = await getDocs(messagesQuery);
          const messages = messagesSnap.docs.map(messageDoc => ({
            id: messageDoc.id,
            ...messageDoc.data(),
            createdAt: (messageDoc.data().createdAt as Timestamp).toDate()
          })) as Message[];

          return {
            id: chatDoc.id,
            ...chatDoc.data(),
            messages,
            updatedAt: chatDoc.data().updatedAt?.toDate(),
            createdAt: chatDoc.data().createdAt?.toDate()
          } as unknown as Chat;
        });

        const chatList = await Promise.all(chatPromises);
        setChats(chatList);
        setLoading(false);
      },
      (err) => {
        console.error('Error fetching chats:', err);
        setError(err as Error);
        setLoading(false);
      }
    );

    return () => unsubscribe();
  }, [userId]);

const sendMessage = useCallback(async (receiverId: string, content: string) => {
  try {
    if (!userId || !receiverId || !content.trim()) {
      throw new Error('Invalid message data');
    }

    // Önce chat oluştur veya bul
    const chatsQuery = query(
      collection(db, 'chats'),
      where('participants', 'array-contains', userId)
    );
    const chatSnapshot = await getDocs(chatsQuery);
    let chatId = '';

    const existingChat = chatSnapshot.docs.find(doc => 
      doc.data().participants.includes(receiverId)
    );

    if (existingChat) {
      chatId = existingChat.id;
    } else {
      // Yeni chat oluştur
      const chatData = {
        participants: [userId, receiverId],
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        lastMessage: null,
        unreadCount: 0
      };
      const newChatRef = await addDoc(collection(db, 'chats'), chatData);
      chatId = newChatRef.id;
    }

    // Mesaj verisini oluştur
    const messageData = {
      chatId,
      senderId: userId,
      receiverId,
      content,
      createdAt: serverTimestamp(),
      read: false
    };

    // Önce mesajı oluştur
    const messageRef = await addDoc(collection(db, 'messages'), messageData);

    // Sonra chat'i güncelle
    await updateDoc(doc(db, 'chats', chatId), {
      lastMessage: {
        content,
        senderId: userId,
        createdAt: serverTimestamp()
      },
      updatedAt: serverTimestamp()
    });

    // Son olarak alıcının unread count'unu güncelle
    await updateDoc(doc(db, 'users', receiverId), {
      unreadMessages: increment(1),
      lastMessageAt: serverTimestamp(),
      lastChatId: chatId
    });

    return messageRef.id;
  } catch (err) {
    console.log('Error sending message:', err);
    throw err;
  }
}, [userId]);


  const markAsRead = useCallback(async (chatId: string) => {
    try {
      const messagesQuery = query(
        collection(db, 'messages'),
        where('chatId', '==', chatId),
        where('receiverId', '==', userId),
        where('read', '==', false)
      );

      const snapshot = await getDocs(messagesQuery);
      const updatePromises = snapshot.docs.map(doc =>
        updateDoc(doc.ref, { read: true })
      );

      await Promise.all(updatePromises);

      // Reset unread count
      const userRef = doc(db, 'users', userId);
      await updateDoc(userRef, {
        unreadMessages: 0
      });
    } catch (err) {
      console.error('Error marking messages as read:', err);
      throw err;
    }
  }, [userId]);

  return {
    chats,
    loading,
    error,
    sendMessage,
    markAsRead
  };
}