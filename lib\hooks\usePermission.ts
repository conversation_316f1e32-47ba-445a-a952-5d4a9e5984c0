import { useAuth } from './useAuth';
import { useSuperAdmin } from './useSuperAdmin';
import type { AdminPermission } from '@/lib/types/firebase';

export function usePermission() {
  const { user } = useAuth();
  const { adminData, isAdmin } = useSuperAdmin(user?.uid);

  const hasPermission = (permission: AdminPermission): boolean => {
    if (!user || !isAdmin || !adminData) return false;
    
    // Superadmin her zaman tüm yetkilere sahiptir
    if (adminData.role === 'superadmin') return true;
    
    // 'all' yetkisi tüm izinleri kapsar
    if (adminData.permissions.includes('all')) return true;
    
    return adminData.permissions.includes(permission);
  };

  const hasAnyPermission = (permissions: AdminPermission[]): boolean => {
    return permissions.some(permission => hasPermission(permission));
  };

  const hasAllPermissions = (permissions: AdminPermission[]): boolean => {
    return permissions.every(permission => hasPermission(permission));
  };

  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    isAdmin,
    adminData
  };
} 