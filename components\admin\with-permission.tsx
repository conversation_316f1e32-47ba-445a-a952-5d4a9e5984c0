import { usePermission } from '@/lib/hooks/usePermission';
import type { AdminPermission } from '@/lib/types/firebase';

interface WithPermissionProps {
  permission: AdminPermission | AdminPermission[];
  fallback?: React.ReactNode;
  requireAll?: boolean;
}

export function WithPermission({
  children,
  permission,
  fallback = null,
  requireAll = false
}: React.PropsWithChildren<WithPermissionProps>) {
  const { hasPermission, hasAllPermissions, hasAnyPermission } = usePermission();

  const checkPermission = () => {
    if (Array.isArray(permission)) {
      return requireAll ? hasAllPermissions(permission) : hasAnyPermission(permission);
    }
    return hasPermission(permission);
  };

  if (!checkPermission()) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
} 