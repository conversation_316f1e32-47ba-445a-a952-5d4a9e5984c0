"use client";

import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ChevronLeft, ChevronRight, Users, Settings, Shield, LayoutDashboard, MessagesSquare, Users2 } from "lucide-react";

const sidebarItems = [
  {
    title: "Dashboard",
    href: "/admin",
    icon: LayoutDashboard,
  },
  {
    title: "Kullanı<PERSON>ılar",
    href: "/admin/users",
    icon: Users,
  },
  {
    title: "Gruplar",
    href: "/admin/groups",
    icon: Users2,
  },
  {
    title: "Moderasyon",
    href: "/admin/moderation",
    icon: Shield,
  },
  {
    title: "Mesajlar",
    href: "/admin/messages",
    icon: MessagesSquare,
  },
  {
    title: "Ayarlar",
    href: "/admin/settings",
    icon: Settings,
  },
];

export function AdminLayoutWrapper({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const [collapsed, setCollapsed] = useState(false);

  // localStorage'dan son durumu yükle
  useEffect(() => {
    const savedState = localStorage.getItem('adminSidebarCollapsed');
    if (savedState !== null) {
      setCollapsed(savedState === 'true');
    }
  }, []);

  // Durumu değiştir ve kaydet
  const toggleSidebar = () => {
    const newState = !collapsed;
    setCollapsed(newState);
    localStorage.setItem('adminSidebarCollapsed', String(newState));
  };

  return (
    <div className="flex h-screen overflow-hidden">
      <aside className={cn(
        "bg-muted flex flex-col border-r transition-all duration-300",
        collapsed ? "w-[60px]" : "w-[240px]"
      )}>
        <div className="flex h-14 items-center justify-between px-4 border-b">
          {!collapsed && <span className="font-semibold">Admin Panel</span>}
          <Button 
            variant="ghost" 
            size="sm"
            onClick={toggleSidebar}
            className={cn(
              "p-0 h-6 w-6",
              collapsed && "ml-auto"
            )}
          >
            {collapsed ? <ChevronRight size={16} /> : <ChevronLeft size={16} />}
          </Button>
        </div>
        <ScrollArea className="flex-1">
          <nav className="flex flex-col gap-1 p-2">
            {sidebarItems.map((item) => {
              const isActive = pathname === item.href;
              return (
                <Link key={item.href} href={item.href}>
                  <span
                    className={cn(
                      "flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground transition-colors",
                      isActive && "bg-accent text-accent-foreground",
                      collapsed && "justify-center px-2"
                    )}
                  >
                    <item.icon size={16} />
                    {!collapsed && item.title}
                  </span>
                </Link>
              );
            })}
          </nav>
        </ScrollArea>
      </aside>
      <main className="flex-1 overflow-y-auto bg-muted/10">
        <div className="p-6">
          {children}
        </div>
      </main>
    </div>
  );
}