import { NextResponse } from 'next/server';

export async function GET() {
  const clientId = process.env.NEXT_PUBLIC_TWITCH_CLIENT_ID;
  const redirectUri = `${process.env.NEXT_PUBLIC_BASE_URL}/api/auth/twitch/callback`;
  const scope = encodeURIComponent('user:read:email user:read:broadcast');

  const twitchAuthUrl = `https://id.twitch.tv/oauth2/authorize?` + 
    `client_id=${clientId}&` +
    `redirect_uri=${encodeURIComponent(redirectUri)}&` +
    `response_type=code&` +
    `scope=${scope}`;

  return NextResponse.redirect(twitchAuthUrl);
} 