"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Icons } from "@/components/icons";
import { cn } from "@/lib/utils";

interface SocialButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  icon: keyof typeof Icons;
  loading?: boolean;
}

export function SocialButton({
  icon,
  children,
  className,
  loading = false,
  ...props
}: SocialButtonProps) {
  const Icon = Icons[icon];

  return (
    <Button
      variant="outline"
      className={cn("w-full", className)}
      disabled={loading}
      {...props}
    >
      {loading ? (
        <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
      ) : (
        <Icon className="mr-2 h-4 w-4" />
      )}
      {children}
    </Button>
  );
}