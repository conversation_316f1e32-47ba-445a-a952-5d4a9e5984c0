"use client";

import { useAuth } from "@/lib/hooks/useAuth";
import { useGroupActions } from "@/lib/hooks/useGroupActions";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Users, Calendar, Shield, Globe2,Lock } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { tr } from "date-fns/locale";
import type { Group } from "@/lib/types/firebase";
import { GroupSettings } from "./group-settings";
import { UploadButton } from "@/components/ui/upload-button";
import Image from "next/image";
import { GroupIcon } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useGroup } from "@/lib/hooks/useGroup";
import { useState } from "react";
import { JoinGroupButton } from "../join-group-button";

interface GroupHeaderProps {
  group: Group;
}

export function GroupHeader({ group: initialGroup }: GroupHeaderProps) {
  const { user } = useAuth();
  const { joinGroup, leaveGroup, cancelJoinRequest, loading } = useGroupActions();
  const { updateGroup } = useGroup();
  const [group, setGroup] = useState(initialGroup);
  const [isUploading, setIsUploading] = useState(false);
  const { success, error, info } = useToast();

  const isAdmin = user && group.admins.includes(user.uid);
  const isMember = user && group.members.includes(user.uid);
  const hasPendingRequest = user && group.pendingRequests?.includes(user.uid);
  const handleImageUpload = async (file: File) => {
    try {
      setIsUploading(true);
      info('Görsel yükleniyor...');

      // Dosya adını oluştur
      const extension = file.name.split('.').pop();
      const fileName = `group-${group.id}-${Date.now()}.${extension}`;
      const publicPath = `/images/groups/${fileName}`;
      
      // Dosyayı public klasörüne kaydet
      const formData = new FormData();
      formData.append('file', file);
      formData.append('path', publicPath);
      
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) throw new Error('Görsel yüklenemedi');

      // Firebase'de grup bilgisini güncelle
      await updateGroup(group.id, {
        image: publicPath
      });

      // Local state'i güncelle
      setGroup(prev => ({
        ...prev,
        image: publicPath
      }));

      success('Grup görseli güncellendi');
    } catch (err) {
      console.error(err);
      error('Görsel güncellenirken bir hata oluştu');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="relative">
      <div className="h-48 w-full bg-gray-100 relative mb-16">
        {group.image ? (
          <Image
            src={group.image}
            alt={group.name}
            fill
            className="object-cover"
            priority
          />
        ) : (
          <div className="flex h-full items-center justify-center">
            <GroupIcon className="h-20 w-20 text-gray-400" />
          </div>
        )}
      
      </div>
      
      <div className="space-y-4 px-4">
        <div className="flex items-start justify-between">
          <div>
            <div className="flex items-center gap-2">
              <h1 className="text-3xl font-bold">{group.name}</h1>
              {isAdmin && (
                <Badge variant="secondary">
                  <Shield className="w-3 h-3 mr-1" />
                  Admin
                </Badge>
              )}
            </div>
            <p className="text-muted-foreground mt-2">{group.description}</p>
          </div>

          <div className="flex items-center gap-2">
            {isAdmin && (
             <><GroupSettings group={group} /><UploadButton
                onUpload={handleImageUpload}
                accept="image/*"
                className="bg-background/90 hover:bg-background backdrop-blur-sm border shadow-sm transition-all"
                disabled={isUploading} /></>
            )}            
            {user && !isAdmin && (
              <div>
                <JoinGroupButton groupId={group.id} type={group.type} />
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center gap-6 text-sm text-muted-foreground">
          <div className="flex items-center gap-2">
            <Users className="w-4 h-4" />
            <span>{group.memberCount} üye</span>
          </div>
          <div className="flex items-center gap-2">
            <Calendar className="w-4 h-4" />
            <span>
              {formatDistanceToNow(group.createdAt, {
                addSuffix: true,
                locale: tr,
              })} oluşturuldu
            </span>
          </div>
          <Badge variant="secondary">{group.category}</Badge>
          <Badge variant="outline">{group.platform}</Badge>          
        </div>
        <div className="flex items-center gap-6 text-sm">
            <h3 className="text-sm font-semibold flex items-center gap-2 text-muted-foreground">
              <Globe2 className="w-4 h-4" />
              Platform : {group.platform}
            </h3>
            <h3 className="text-sm font-semibold flex items-center gap-2 text-muted-foreground">
                <Lock className="w-4 h-4" />
                Grup Tipi : {group.type === "public" ? "Herkese Açık" : "Özel (Onay Gerekli)"}
              </h3>
          </div>
          <div className="flex items-center gap-6 text-sm">
            {group.tags && group.tags.length > 0 && (
              <div>
                <h3 className="text-sm font-semibold text-muted-foreground mb-2">ETIKETLER</h3>
                <div className="flex flex-wrap gap-2">
                  {group.tags.map((tag: string) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
      </div>
    </div>
  );
}