"use client";

import { useState } from "react";
import { useAuth } from "@/lib/hooks/useAuth";
import { GroupList } from "@/components/groups/group-list";
import { GroupFilters } from "@/components/groups/group-filters";
import { SearchBar } from "@/components/groups/search-bar";
import { CreateGroupDialog } from "@/components/groups/create-group-dialog";

export function GroupsPageClient() {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);

  const handleCategoryChange = (category: string) => {
    setSelectedCategories(prev =>
      prev.includes(category)
        ? prev.filter(c => c !== category)
        : [...prev, category]
    );
  };

  const handlePlatformChange = (platform: string) => {
    setSelectedPlatforms(prev =>
      prev.includes(platform)
        ? prev.filter(p => p !== platform)
        : [...prev, platform]
    );
  };

  return (
    <main className="container mx-auto max-w-6xl py-8">
      <div className="flex flex-col gap-6">
        <div className="flex items-center justify-between md:flex-row flex-col">
          <div className="space-y-1">
            <h1 className="text-3xl font-bold tracking-tight">Oyun Grupları</h1>
            <p className="text-muted-foreground p-4">
              İlgi alanlarına göre grupları keşfet ve topluluklara katıl.
            </p>
          </div>
          {user && <CreateGroupDialog />}
        </div>
        
        <SearchBar onSearch={setSearchQuery} />
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <aside className="md:col-span-1">
            <GroupFilters
              selectedCategories={selectedCategories}
              selectedPlatforms={selectedPlatforms}
              onCategoryChange={handleCategoryChange}
              onPlatformChange={handlePlatformChange}
            />
          </aside>
          
          <div className="md:col-span-3">
            <GroupList
              searchQuery={searchQuery}
              filters={{
                categories: selectedCategories,
                platforms: selectedPlatforms,
              }}
            />
          </div>
        </div>
      </div>
    </main>
  );
}