import { db } from '@/lib/firebase';
import { 
  collection, 
  addDoc, 
  query, 
  where, 
  orderBy, 
  onSnapshot,
  serverTimestamp,
  getDocs
} from 'firebase/firestore';

export interface GroupMessage {
  id: string;
  content: string;
  senderId: string;
  senderName: string;
  groupId: string;
  timestamp: Date;
  type: 'text' | 'image' | 'file';
}

export const sendGroupMessage = async (
  groupId: string, 
  content: string, 
  senderId: string,
  senderName: string,
  type: 'text' | 'image' | 'file' = 'text'
) => {
  try {
    // Mesajı ekle
    const messageRef = await addDoc(collection(db, 'groupMessages'), {
      content,
      senderId,
      senderName,
      groupId,
      timestamp: serverTimestamp(),
      type,
    });

    // Grup üyelerini al
    const groupDoc = await getDocs(collection(db, 'groups'));
    const group = groupDoc.docs.find(doc => doc.id === groupId);
    const members = group?.data().members || [];

    // Her üye için bildirim oluştur (mesajı gönderen hariç)
    const notifications = members
      .filter((memberId: string) => memberId !== senderId)
      .map((userId: string) => ({
        userId,
        groupId,
        messageId: messageRef.id,
        timestamp: serverTimestamp(),
        read: false,
      }));

    // Bildirimleri toplu olarak ekle
    const notificationsRef = collection(db, 'groupNotifications');
    notifications.forEach(async (notification: any) => {
      await addDoc(notificationsRef, notification);
    });

    return messageRef.id;
  } catch (error) {
    console.error('Error sending group message:', error);
    throw error;
  }
};

export const subscribeToGroupMessages = (
  groupId: string, 
  callback: (messages: GroupMessage[]) => void
) => {
  const q = query(
    collection(db, 'groupMessages'),
    where('groupId', '==', groupId),
    orderBy('timestamp', 'asc')
  );

  return onSnapshot(q, (snapshot) => {
    const messages = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as GroupMessage[];
    callback(messages);
  });
}; 