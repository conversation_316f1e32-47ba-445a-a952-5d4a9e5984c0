import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import path from 'path';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const userId = formData.get('userId') as string;

    if (!file || !userId) {
      return NextResponse.json(
        { error: 'Dosya veya kullanıcı ID\'si eksik' },
        { status: 400 }
      );
    }

    // Dosya uzantısını al
    const extension = path.extname(file.name);
    
    // Dosya yolu oluştur
    const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'profile-photos');
    const fileName = `${userId}${extension}`;
    const filePath = path.join(uploadDir, fileName);

    // Klasörü oluştur (yoksa)
    try {
      await mkdir(uploadDir, { recursive: true });
    } catch (error) {
      console.error('Klasör oluşturma hatası:', error);
    }

    // Dosyayı byte array'e çevir
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Dosyayı kaydet
    await writeFile(filePath, buffer);

    // URL'i döndür
    const url = `/uploads/profile-photos/${fileName}`;

    return NextResponse.json({ url });
  } catch (error) {
    console.error('Dosya yükleme hatası:', error);
    return NextResponse.json(
      { error: 'Dosya yüklenirken bir hata oluştu' },
      { status: 500 }
    );
  }
}

export const config = {
  api: {
    bodyParser: false,
  },
}; 