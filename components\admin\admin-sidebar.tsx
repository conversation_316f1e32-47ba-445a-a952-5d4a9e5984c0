"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { usePermission } from "@/lib/hooks/usePermission";
import {
  Users,
  LayoutDashboard,
  Settings,
  Shield,
  MessageSquare,
  BarChart3,
  AlertTriangle,
} from "lucide-react";
import type { AdminPermission } from "@/lib/types/firebase";

interface MenuItem {
  title: string;
  icon: React.ElementType;
  href: string;
  permission: AdminPermission | AdminPermission[];
}

const menuItems: MenuItem[] = [
  {
    title: "Dashboard",
    icon: LayoutDashboard,
    href: "/admin",
    permission: "all",
  },
  {
    title: "Kullanı<PERSON>ılar",
    icon: Users,
    href: "/admin/users",
    permission: ["users.view", "users.manage"],
  },
  {
    title: "Gruplar",
    icon: MessageSquare,
    href: "/admin/groups",
    permission: ["groups.view", "groups.manage"],
  },
  {
    title: "<PERSON><PERSON><PERSON>",
    icon: Bar<PERSON>hart3,
    href: "/admin/reports",
    permission: ["reports.view", "reports.manage"],
  },
  {
    title: "Moderasyon",
    icon: Shield,
    href: "/admin/moderation",
    permission: ["users.manage", "groups.manage"],
  },
  {
    title: "Uyarılar",
    icon: AlertTriangle,
    href: "/admin/alerts",
    permission: ["users.manage", "groups.manage"],
  },
  {
    title: "Ayarlar",
    icon: Settings,
    href: "/admin/settings",
    permission: "settings.manage",
  },
];

export function AdminSidebar() {
  const pathname = usePathname();
  const { hasAnyPermission } = usePermission();

  // Kullanıcının erişebileceği menü öğelerini filtrele
  const filteredMenuItems = menuItems.filter(item => 
    Array.isArray(item.permission) 
      ? hasAnyPermission(item.permission)
      : hasAnyPermission([item.permission])
  );

  return (
    <aside className="w-64 border-r bg-background">
      <div className="h-16 border-b flex items-center px-6">
        <h1 className="text-lg font-bold">Admin Panel</h1>
      </div>
      <nav className="p-4 space-y-2">
        {filteredMenuItems.map((item) => {
          const Icon = item.icon;
          return (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "flex items-center gap-3 px-3 py-2 rounded-md text-sm transition-colors",
                pathname === item.href
                  ? "bg-primary text-primary-foreground"
                  : "hover:bg-muted"
              )}
            >
              <Icon className="h-4 w-4" />
              {item.title}
            </Link>
          );
        })}
      </nav>
    </aside>
  );
}