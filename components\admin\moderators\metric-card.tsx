import { Card } from "@/components/ui/card";
import { LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface MetricCardProps {
  title: string;
  value: number;
  icon: LucideIcon;
  trend?: {
    value: number;
    label: string;
  };
  loading?: boolean;
}

export function MetricCard({
  title,
  value,
  icon: Icon,
  trend,
  loading = false
}: MetricCardProps) {
  return (
    <Card className="p-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="font-semibold">{title}</h3>
          {loading ? (
            <div className="h-9 w-24 bg-muted animate-pulse rounded mt-2" />
          ) : (
            <p className="text-3xl font-bold mt-2">
              {value.toLocaleString()}
            </p>
          )}
          {trend && (
            <p className={cn(
              "text-sm mt-2",
              trend.value > 0 ? "text-green-600" : "text-red-600"
            )}>
              {trend.value > 0 ? "+" : ""}{trend.value}% {trend.label}
            </p>
          )}
        </div>
        <Icon className="h-8 w-8 text-primary" />
      </div>
    </Card>
  );
} 