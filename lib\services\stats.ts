import { collection, getDocs, query, where, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';

interface Stats {
  activeUsers: number;
  activeGroups: number;
  lastUpdated: number;
}

let cachedStats: Stats | null = null;
const CACHE_DURATION = 3 * 60 * 1000; // 3 minutes in milliseconds

export async function getStats(): Promise<Stats> {
  // Return cached data if it exists and is not expired
  if (cachedStats && Date.now() - cachedStats.lastUpdated < CACHE_DURATION) {
    return cachedStats;
  }

  try {
    // Fetch active groups count
    const groupsQuery = query(collection(db, 'groups'));
    const groupsSnapshot = await getDocs(groupsQuery);
    const activeGroups = groupsSnapshot.size;

    // Fetch active users count
    const thirtyDaysAgo = Timestamp.fromDate(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000));
    const usersQuery = query(
      collection(db, 'users'),
      where('lastLogin', '>=', thirtyDaysAgo)
    );
    const usersSnapshot = await getDocs(usersQuery);
    const activeUsers = usersSnapshot.size;

    // Update cache
    cachedStats = {
      activeUsers,
      activeGroups,
      lastUpdated: Date.now(),
    };

    return cachedStats;
  } catch (error) {
    console.error('Error fetching stats:', error);
    
    // Return last cached data if available, otherwise return default values
    return cachedStats || {
      activeUsers: 0,
      activeGroups: 0,
      lastUpdated: Date.now(),
    };
  }
}