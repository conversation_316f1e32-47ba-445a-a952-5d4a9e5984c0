import { useUnreadMessages } from '@/hooks/use-unread-messages';
import { useAuth } from '@/lib/hooks/useAuth';
import Link from 'next/link';
import { MessageSquare } from 'lucide-react';

export function Navbar() {
  const { user } = useAuth();
  const hasUnread = useUnreadMessages(user);
  
  return (
    <Link href="/messages" className="relative">
      <MessageSquare className="h-5 w-5" />
      {hasUnread && (
        <span className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-red-500" />
      )}
    </Link>
  );
} 