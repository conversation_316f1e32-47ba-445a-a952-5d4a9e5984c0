import { NextResponse } from 'next/server';
import { auth, db } from "@/lib/firebase";
import { doc, updateDoc, getDoc } from 'firebase/firestore';

const MAX_RETRIES = 5;
const RETRY_DELAY = 3000; // 3 saniye

async function waitForAuth(retries = 0): Promise<string> {
  if (retries >= MAX_RETRIES) {
    throw new Error('Authentication timeout - user not found');
  }

  const currentUser = auth.currentUser;
  if (currentUser) {
    return currentUser.uid;
  }

  // Bekle ve tekrar dene
  await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
  return waitForAuth(retries + 1);
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    
    if (!code) {
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_BASE_URL}/profile?error=no_code`);
    }

    // <PERSON><PERSON> say<PERSON>ya <PERSON>, Twitch verilerini almak için
    const redirectUrl = `${process.env.NEXT_PUBLIC_BASE_URL}/profile?twitch_code=${code}`;
    return NextResponse.redirect(redirectUrl);

  } catch (error: any) {
    console.error('Twitch connection error:', error);
    return NextResponse.redirect(
      `${process.env.NEXT_PUBLIC_BASE_URL}/profile?error=connection_failed&message=${encodeURIComponent(error.message)}`
    );
  }
}
