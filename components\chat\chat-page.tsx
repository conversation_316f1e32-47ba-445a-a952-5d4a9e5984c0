"use client";

import { useState, useEffect, useRef } from 'react';
import { useAuth } from '@/lib/hooks/useAuth';
import { useRouter } from 'next/navigation';
import { useMessages } from '@/lib/hooks/useMessages';
import { useUserInfo } from '@/lib/hooks/useUserInfo';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Send, ArrowLeft } from 'lucide-react';
import { Loader2 } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Chat, Message } from '@/lib/types/firebase';

interface ChatPageProps {
  chatId: string;
}

export function ChatPage({ chatId }: ChatPageProps) {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const { sendMessage, markAsRead, chats, loading: messagesLoading } = useMessages(user?.uid || '');
  const [newMessage, setNewMessage] = useState('');
  const [sending, setSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const chat = chats.find(c => c.id === chatId) as Chat | undefined;
  const otherParticipant = chat?.participants.find(id => id !== user?.uid);
  const { userInfo: otherUserInfo } = useUserInfo(otherParticipant);

  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/auth/sign-in');
    }
  }, [user, authLoading, router]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chat?.messages]);

  useEffect(() => {
    if (user && chatId) {
      markAsRead(chatId);
    }
  }, [user, chatId, markAsRead]);

  if (authLoading || !user) {
    return null;
  }

  if (messagesLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() || !otherParticipant) return;

    setSending(true);
    try {
      await sendMessage(otherParticipant, newMessage.trim());
      setNewMessage('');
    } catch (error) {
      
      if (error instanceof Error && 
         (error.message.includes('permission') || error.message.includes('insufficient'))) {
        setNewMessage('');
      } else {
        console.error('Mesaj gönderilirken bir hata oluştu:', error);
      }
    } finally {
      setSending(false);
    }
  };

  return (
    <main className="container max-w-2xl mx-auto py-4 px-4">
      <div className="flex flex-col h-[calc(100vh-8rem)]">
        {/* Header */}
        <div className="flex items-center gap-4 mb-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push('/messages')}
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <Avatar>
            <AvatarImage
              src={otherUserInfo?.photoURL || `https://api.dicebear.com/7.x/avataaars/svg?seed=${otherParticipant}`}
            />
            <AvatarFallback>{otherUserInfo?.nickname?.[0] || '?'}</AvatarFallback>
          </Avatar>
          <div>
            <h1 className="font-semibold">{otherUserInfo?.nickname || 'Yükleniyor...'}</h1>
            <p className="text-sm text-muted-foreground">@{otherUserInfo?.username}</p>
          </div>
        </div>

        {/* Messages */}
        <Card className="flex-1 p-4 mb-4 overflow-y-auto">
          <div className="space-y-4">
            {messagesLoading && (
              <div className="flex justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            )}
            {chat?.messages?.map((message) => (
              <div
                key={message.id}
                className={`flex ${
                  message.senderId === user.uid ? 'justify-end' : 'justify-start'
                }`}
              >
                <div
                  className={`max-w-[70%] ${
                    message.senderId === user.uid
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-muted'
                  } rounded-lg px-4 py-2`}
                >
                  <p>{message.content}</p>
                  <p className="text-xs opacity-70 mt-1">
                    {formatDistanceToNow(message.createdAt, {
                      addSuffix: true,
                      locale: tr,
                    })}
                  </p>
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>
        </Card>

        {/* Message Input */}
        <form onSubmit={handleSendMessage} className="flex gap-2">
          <Input
            autoComplete="off"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            placeholder="Mesajınızı yazın..."
            disabled={sending}
          />
          <Button 
            type="submit" 
            disabled={sending || !newMessage.trim() || messagesLoading}
          >
            <Send className="h-4 w-4" />
          </Button>
        </form>
      </div>
    </main>
  );
}