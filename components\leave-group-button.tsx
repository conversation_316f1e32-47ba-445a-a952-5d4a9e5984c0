'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { leaveGroup } from '@/lib/firebase/group';
import { useRouter } from 'next/navigation';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

interface LeaveGroupButtonProps {
  groupId: string;
  userId: string;
}

export function LeaveGroupButton({ groupId, userId }: LeaveGroupButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleLeaveGroup = async () => {
    setIsLoading(true);
    try {
      const success = await leaveGroup(groupId, userId);
      if (success) {
        router.push('/groups');
        router.refresh();
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button variant="destructive" disabled={isLoading}>
          {isLoading ? 'Ayrılıyor...' : 'Gruptan Ayrıl'}
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            Gruptan ayrılmak istediğinize emin misiniz?
          </AlertDialogTitle>
          <AlertDialogDescription>
            Bu işlem geri alınamaz. Gruptan ayrıldıktan sonra tekrar katılmak
            için yeni bir istek göndermeniz gerekebilir.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>İptal</AlertDialogCancel>
          <AlertDialogAction onClick={handleLeaveGroup} disabled={isLoading}>
            {isLoading ? 'Ayrılıyor...' : 'Ayrıl'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
