import { notFound } from 'next/navigation';
import { ChatPage } from '@/components/chat/chat-page';
import { Suspense } from 'react';
import { Loader2 } from 'lucide-react';

interface PageProps {
  params: {
    chatId: string;
  };
}

// Server Component olarak işaretle
export const dynamic = 'force-dynamic';
export const revalidate = 0;

// Ana sayfa component'i
export default function Page({ params }: PageProps) {
  return (
    <Suspense
      fallback={
        <div className="flex items-center justify-center min-h-screen">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      }
    >
      <ChatPage chatId={String(params.chatId)} />
    </Suspense>
  );
}

// Static parametre oluşturma
export async function generateStaticParams() {
  return [];
}