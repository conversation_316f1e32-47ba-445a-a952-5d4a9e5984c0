import { FirebaseError } from 'firebase/app';
import { auth, db, storage } from '@/lib/firebase';
import { doc, getDoc, updateDoc, collection, query, where, getDocs, limit } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { updateProfile, User } from 'firebase/auth';

// Loglama için yardımcı fonksiyon
const logOperation = (operation: string, details: any) => {
  console.log(`[Firebase Operation] ${operation}:`, details);
};

const logError = (operation: string, error: any) => {
  if (error instanceof FirebaseError) {
    console.error(`[Firebase Error] ${operation}:`, {
      code: error.code,
      message: error.message,
      details: error,
    });
  } else {
    console.error(`[Firebase Error] ${operation}:`, error);
  }
};

// Kullanıcı işlemleri
export const userOperations = {
  // Profil fotoğrafı güncelleme
  async updateProfilePhoto(user: User, file: File) {
    try {
      logOperation('updateProfilePhoto', { userId: user.uid });

      const storageRef = ref(storage, `avatars/${user.uid}`);
      await uploadBytes(storageRef, file);
      const photoURL = await getDownloadURL(storageRef);

      await updateProfile(user, { photoURL });
      await updateDoc(doc(db, "users", user.uid), { photoURL });

      logOperation('updateProfilePhoto', { success: true, photoURL });
      return photoURL;
    } catch (error) {
      logError('updateProfilePhoto', error);
      throw error;
    }
  },

  // Kullanıcı verilerini getirme
  async getUserData(userId: string) {
    try {
      logOperation('getUserData', { userId });

      const userDoc = await getDoc(doc(db, "users", userId));
      if (!userDoc.exists()) {
        logOperation('getUserData', { exists: false });
        return null;
      }

      const userData = userDoc.data();
      logOperation('getUserData', { success: true, data: userData });
      return userData;
    } catch (error) {
      logError('getUserData', error);
      throw error;
    }
  },

  // Hesap bağlantılarını güncelleme
  async updateLinkedAccounts(userId: string, platform: string, accountData: any) {
    try {
      logOperation('updateLinkedAccounts', { userId, platform, accountData });

      const userRef = doc(db, "users", userId);
      const userDoc = await getDoc(userRef);

      if (!userDoc.exists()) {
        throw new Error('User document not found');
      }

      const currentData = userDoc.data();
      await updateDoc(userRef, {
        linkedAccounts: {
          ...currentData.linkedAccounts,
          [platform]: accountData
        }
      });

      logOperation('updateLinkedAccounts', { success: true });
      return true;
    } catch (error) {
      logError('updateLinkedAccounts', error);
      throw error;
    }
  },

  // Kullanıcı gizlilik ayarlarını güncelleme
  async updatePrivacySettings(userId: string, isPublic: boolean) {
    try {
      logOperation('updatePrivacySettings', { userId, isPublic });

      await updateDoc(doc(db, "users", userId), { isPublic });
      
      logOperation('updatePrivacySettings', { success: true });
      return true;
    } catch (error) {
      logError('updatePrivacySettings', error);
      throw error;
    }
  },

  // Username ile kullanıcı arama
  async getUserByUsername(username: string) {
    try {
      logOperation('getUserByUsername', { username });

      const usersRef = collection(db, 'users');
      const q = query(usersRef, where('username', '==', username), limit(1));
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        logOperation('getUserByUsername', { exists: false });
        return null;
      }

      const userData = querySnapshot.docs[0].data();
      logOperation('getUserByUsername', { success: true, data: userData });
      return userData;
    } catch (error) {
      logError('getUserByUsername', error);
      throw error;
    }
  }
};

// Platform bağlantı işlemleri
export const platformOperations = {
  // Discord bağlantısı
  async connectDiscord(userId: string, discordData: any) {
    try {
      logOperation('connectDiscord', { userId });
      return await userOperations.updateLinkedAccounts(userId, 'discord', discordData);
    } catch (error) {
      logError('connectDiscord', error);
      throw error;
    }
  },

  // Twitch bağlantısı
  async connectTwitch(userId: string, twitchData: any) {
    try {
      logOperation('connectTwitch', { userId });
      return await userOperations.updateLinkedAccounts(userId, 'twitch', twitchData);
    } catch (error) {
      logError('connectTwitch', error);
      throw error;
    }
  }
}; 