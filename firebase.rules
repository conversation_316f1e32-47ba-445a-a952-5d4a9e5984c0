rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper Functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Super Admins Collection Rules
    match /superadmins/{adminId} {
      allow read: if isAuthenticated() && (isSuperAdmin() || request.auth.uid == adminId);
      allow create: if isAuthenticated() && !exists(/databases/$(database)/documents/superadmins/$(request.auth.uid));
      allow update: if isAuthenticated() && (isSuperAdmin() || request.auth.uid == adminId);
      allow delete: if isAuthenticated() && isSuperAdmin();
    }

    function isSuperAdmin() {
      let adminDoc = get(/databases/$(database)/documents/superadmins/$(request.auth.uid));
      return adminDoc != null && adminDoc.data.role == "superadmin" && adminDoc.data.status == "active";
    }

    function isModerator() {
      let moderatorDoc = get(/databases/$(database)/documents/moderators/$(request.auth.uid));
      return moderatorDoc != null && moderatorDoc.data.status == "active";
    }

    function hasModeratorPermission(permission) {
      let moderatorDoc = get(/databases/$(database)/documents/moderators/$(request.auth.uid));
      return moderatorDoc != null && moderatorDoc.data.status == "active" && (
        moderatorDoc.data.permissions.hasAny([permission, 'all']) ||
        moderatorDoc.data.role == "admin"
      );
    }
    
    // Collection Access Helper
    function canAccessCollection() {
      return isAuthenticated() && (isSuperAdmin() || isMasterAdmin());
    }

    // Master Admin UID Kontrolü
    function isMasterAdmin() {
      return request.auth.uid == "fuZvVkFyumMwjsEsbwHzCSJM3RS2";
    }

    // Moderators Collection Rules
    match /moderators/{moderatorId} {
      allow read: if isAuthenticated();
      allow create: if isSuperAdmin();
      allow update: if isSuperAdmin() || 
        (request.auth.uid == moderatorId && 
         request.resource.data.diff(resource.data).affectedKeys()
           .hasOnly(['lastActive', 'lastLoginAt']));
      allow delete: if isSuperAdmin();
    }

    // Users Collection Rules
    match /users/{userId} {
      allow read: if true;
      allow write: if isAuthenticated() && (
        request.auth.uid == userId || 
        isSuperAdmin() || 
        isMasterAdmin() ||
        (isModerator() && hasModeratorPermission('users.manage'))
      );
    }

    // Groups Collection Rules
    match /groups/{groupId} {
      allow read: if true;
      allow create: if isAuthenticated();
      allow update: if isAuthenticated() && (
        isGroupAdmin(groupId) || 
        canAccessCollection() ||
        (isModerator() && hasModeratorPermission('groups.manage')) ||
        (
          request.auth.uid in resource.data.members &&
          !request.resource.data.diff(resource.data).affectedKeys()
            .hasAny(['name', 'description', 'category', 'platform', 'type', 'admins', 'pendingRequests']) &&
          resource.data.members.hasAll(request.resource.data.members)
        ) ||
        (
          (!request.resource.data.diff(resource.data).affectedKeys()
            .hasAny(['name', 'description', 'category', 'platform', 'type', 'admins'])) &&
          (
            (resource.data.type == 'public' &&
             request.resource.data.members.hasAll(resource.data.members) &&
             request.resource.data.members.removeAll(resource.data.members).size() <= 1 &&
             request.resource.data.members.removeAll(resource.data.members).hasOnly([request.auth.uid])) ||
            (resource.data.type == 'private' &&
             request.resource.data.pendingRequests.hasAll(resource.data.pendingRequests) &&
             request.resource.data.pendingRequests.removeAll(resource.data.pendingRequests).size() <= 1 &&
             request.resource.data.pendingRequests.removeAll(resource.data.pendingRequests).hasOnly([request.auth.uid]))
          )
        )
      );
      allow delete: if isGroupAdmin(groupId) || canAccessCollection() || 
        (isModerator() && hasModeratorPermission('groups.manage'));
    }

    // Rate Limiting Helper
    function isWithinRateLimit() {
      let recentAttempts = get(/databases/$(database)/documents/adminLogs/$(request.auth.uid)).data.recentAttempts;
      return recentAttempts == null || recentAttempts < 100;
    }

    // Admin Logs Collection Rules
    match /adminLogs/{logId} {
      allow read: if isAuthenticated() && (isSuperAdmin() || isModerator());
      allow create: if isAuthenticated() && (isSuperAdmin() || isModerator()) && isWithinRateLimit();
      allow update, delete: if false;
    }

    // Alerts Collection Rules
    match /alerts/{alertId} {
      allow read: if isAuthenticated() && (isSuperAdmin() || isModerator());
      allow create: if isAuthenticated() && (isSuperAdmin() || 
        (isModerator() && hasModeratorPermission('alerts.manage'))) && 
        request.resource.data.keys().hasAll(['type', 'message', 'timestamp', 'status']) &&
        request.resource.data.type in ['info', 'warning', 'error'] &&
        request.resource.data.status in ['active', 'resolved'];
      allow update: if isAuthenticated() && (isSuperAdmin() || 
        (isModerator() && hasModeratorPermission('alerts.manage')));
      allow delete: if isAuthenticated() && isSuperAdmin();
    }

    // Reports Collection Rules
    match /reports/{reportId} {
      allow read: if isAuthenticated() && (isSuperAdmin() || 
        (isModerator() && hasModeratorPermission('reports.view')));
      allow create: if isAuthenticated();
      allow update: if isAuthenticated() && (isSuperAdmin() || 
        (isModerator() && hasModeratorPermission('reports.manage')));
      allow delete: if isAuthenticated() && isSuperAdmin();
    }

    function isGroupAdmin(groupId) {
      let group = get(/databases/$(database)/documents/groups/$(groupId)).data;
      return request.auth.uid in group.admins;
    }

    function isGroupMember(groupId) {
      let group = get(/databases/$(database)/documents/groups/$(groupId)).data;
      return request.auth.uid in group.members;
    }

    // Messages Collection Rules
    match /messages/{messageId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
      allow update: if isAuthenticated() && (
        request.auth.uid == resource.data.senderId ||
        request.auth.uid == resource.data.receiverId
      );
      allow delete: if isAuthenticated() && (
        request.auth.uid == resource.data.senderId
      );
    }
    
    match /settings/{documentId} {
      allow read, write: if isSuperAdmin();
    }

    // Chats Collection Rules
    match /chats/{chatId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
      allow update: if isAuthenticated() && (
        request.auth.uid in resource.data.participants ||
        request.auth.uid == resource.data.senderId ||
        request.auth.uid == resource.data.receiverId
      );
      allow delete: if false;
    }
  }
}

service firebase.storage {
  match /b/{bucket}/o {
    match /groups/{groupId}/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && (
        isSuperAdmin() ||
        (isModerator() && hasModeratorPermission('groups.manage')) ||
        exists(/databases/$(database)/documents/groups/$(groupId)) &&
        get(/databases/$(database)/documents/groups/$(groupId)).data.admins[request.auth.uid] == true
      );
    }
  }
}