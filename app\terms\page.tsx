import { Card } from "@/components/ui/card";

export default function TermsPage() {
  return (
    <main className="container max-w-6xl mx-auto py-16 px-4">
      <Card className="p-8">
        <h1 className="text-3xl font-bold mb-8">Kullanım Koşulları</h1>
        
        <div className="prose prose-neutral dark:prose-invert max-w-none space-y-6">
          <p>
            oyuncubul.com'u kullanarak aşağıdaki kullanım koşullarını kabul etmiş sayılırsınız.
            Lütfen bu koşulları dikkatlice okuyunuz.
          </p>

          <h2 className="text-2xl font-semibold mt-8">Hesap Oluşturma</h2>
          <ul className="list-disc pl-6 space-y-2">
            <li>13 yaşından büyük olmanız gerekmektedir</li>
            <li>Doğ<PERSON> ve güncel bilgiler sağlamalısınız</li>
            <li>Hesap güvenliğinizden siz sorumlusunuz</li>
            <li>Başkasının kimliğine bürünmek yasaktır</li>
          </ul>

          <h2 className="text-2xl font-semibold mt-8">Kullanıcı Davranışları</h2>
          <p>
            Aşağıdaki davranışlar kesinlikle yasaktır:
          </p>
          <ul className="list-disc pl-6 space-y-2">
            <li>Spam veya istenmeyen içerik paylaşımı</li>
            <li>Taciz veya zorbalık</li>
            <li>Nefret söylemi</li>
            <li>Yasadışı içerik paylaşımı</li>
            <li>Platformun kötüye kullanımı</li>
          </ul>

          <h2 className="text-2xl font-semibold mt-8">İçerik Politikası</h2>
          <p>
            Paylaştığınız tüm içerikler şu kurallara uymalıdır:
          </p>
          <ul className="list-disc pl-6 space-y-2">
            <li>Telif haklarına saygılı olmalı</li>
            <li>Topluluk kurallarına uygun olmalı</li>
            <li>Yanıltıcı olmamalı</li>
            <li>Zararlı yazılım içermemeli</li>
          </ul>

          <h2 className="text-2xl font-semibold mt-8">Hizmet Değişiklikleri</h2>
          <p>
            oyuncubul.com, sunduğu hizmetlerde değişiklik yapma hakkını saklı tutar.
            Önemli değişiklikler kullanıcılara bildirilecektir.
          </p>

          <h2 className="text-2xl font-semibold mt-8">Hesap Sonlandırma</h2>
          <p>
            Kullanım koşullarının ihlali durumunda hesabınız askıya alınabilir veya
            kalıcı olarak kapatılabilir.
          </p>

          <h2 className="text-2xl font-semibold mt-8">Sorumluluk Reddi</h2>
          <p>
            Platform üzerindeki kullanıcı etkileşimlerinden ve içeriklerinden
            oyuncubul.com sorumlu tutulamaz.
          </p>

          <p className="text-sm text-muted-foreground mt-8">
            Son güncelleme: {new Date().toLocaleDateString()}
          </p>
        </div>
      </Card>
    </main>
  );
}