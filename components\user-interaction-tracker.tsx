'use client';

import { useEffect } from 'react';

export function UserInteractionTracker() {
  useEffect(() => {
    const handleFirstInteraction = () => {
      document.documentElement.setAttribute('data-user-interacted', 'true');
      // Remove the listener after first interaction
      document.removeEventListener('click', handleFirstInteraction);
      document.removeEventListener('keydown', handleFirstInteraction);
    };

    document.addEventListener('click', handleFirstInteraction);
    document.addEventListener('keydown', handleFirstInteraction);

    return () => {
      document.removeEventListener('click', handleFirstInteraction);
      document.removeEventListener('keydown', handleFirstInteraction);
    };
  }, []);

  return null;
} 