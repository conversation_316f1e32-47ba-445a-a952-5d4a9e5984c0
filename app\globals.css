@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.75rem;
  }

  /* Varsay<PERSON><PERSON> aydın<PERSON><PERSON><PERSON> tema */
  [data-theme="light"] {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
  }

  /* Varsayılan koyu tema */
  [data-theme="dark"] {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }

  /* Gül teması - Aydınlık */
  [data-theme="rose"] {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 346.8 77.2% 49.8%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 346.8 77.2% 95%;
    --secondary-foreground: 346.8 77.2% 20%;
    --muted: 346.8 77.2% 95%;
    --muted-foreground: 346.8 77.2% 40%;
    --accent: 346.8 77.2% 95%;
    --accent-foreground: 346.8 77.2% 20%;
    --border: 346.8 77.2% 90%;
    --input: 346.8 77.2% 90%;
    --ring: 346.8 77.2% 49.8%;
  }

  /* Gül teması - Koyu */
  [data-theme="rose-dark"] {
    --background: 346.8 77.2% 5%;
    --foreground: 346.8 77.2% 98%;
    --card: 346.8 77.2% 5%;
    --card-foreground: 346.8 77.2% 98%;
    --popover: 346.8 77.2% 5%;
    --popover-foreground: 346.8 77.2% 98%;
    --primary: 346.8 77.2% 49.8%;
    --primary-foreground: 346.8 77.2% 98%;
    --secondary: 346.8 77.2% 15%;
    --secondary-foreground: 346.8 77.2% 98%;
    --muted: 346.8 77.2% 15%;
    --muted-foreground: 346.8 77.2% 65%;
    --accent: 346.8 77.2% 15%;
    --accent-foreground: 346.8 77.2% 98%;
    --border: 346.8 77.2% 15%;
    --input: 346.8 77.2% 15%;
    --ring: 346.8 77.2% 49.8%;
  }

  /* Yeşil teması - Aydınlık */
  [data-theme="green"] {
    --background: 0 0% 100%;
    --foreground: 142.1 76.2% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 142.1 76.2% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 142.1 76.2% 4.9%;
    --primary: 142.1 76.2% 36.3%;
    --primary-foreground: 0 0% 100%;
    --secondary: 142.1 76.2% 95%;
    --secondary-foreground: 142.1 76.2% 20%;
    --muted: 142.1 76.2% 95%;
    --muted-foreground: 142.1 76.2% 40%;
    --accent: 142.1 76.2% 95%;
    --accent-foreground: 142.1 76.2% 20%;
    --border: 142.1 76.2% 90%;
    --input: 142.1 76.2% 90%;
    --ring: 142.1 76.2% 36.3%;
  }

  /* Yeşil teması - Koyu */
  [data-theme="green-dark"] {
    --background: 142.1 76.2% 5%;
    --foreground: 142.1 76.2% 98%;
    --card: 142.1 76.2% 5%;
    --card-foreground: 142.1 76.2% 98%;
    --popover: 142.1 76.2% 5%;
    --popover-foreground: 142.1 76.2% 98%;
    --primary: 142.1 76.2% 36.3%;
    --primary-foreground: 0 0% 100%;
    --secondary: 142.1 76.2% 15%;
    --secondary-foreground: 142.1 76.2% 98%;
    --muted: 142.1 76.2% 15%;
    --muted-foreground: 142.1 76.2% 65%;
    --accent: 142.1 76.2% 15%;
    --accent-foreground: 142.1 76.2% 98%;
    --border: 142.1 76.2% 15%;
    --input: 142.1 76.2% 15%;
    --ring: 142.1 76.2% 36.3%;
  }

  /* Diğer temalar için de benzer şekilde aydınlık ve koyu modlar eklenecek */

  [data-theme="purple"] {
    --background: 0 0% 100%;
    --foreground: 270 91% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 270 91% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 270 91% 4.9%;
    --primary: 270 91% 56%;
    --primary-foreground: 0 0% 100%;
    --secondary: 270 91% 95%;
    --secondary-foreground: 270 91% 20%;
    --muted: 270 91% 95%;
    --muted-foreground: 270 91% 40%;
    --accent: 270 91% 95%;
    --accent-foreground: 270 91% 20%;
    --border: 270 91% 90%;
    --input: 270 91% 90%;
    --ring: 270 91% 56%;
  }

  /* Mor teması - Koyu */
  [data-theme="purple-dark"] {
    --background: 270 91% 5%;
    --foreground: 270 91% 98%;
    --card: 270 91% 5%;
    --card-foreground: 270 91% 98%;
    --popover: 270 91% 5%;
    --popover-foreground: 270 91% 98%;
    --primary: 270 91% 56%;
    --primary-foreground: 0 0% 100%;
    --secondary: 270 91% 15%;
    --secondary-foreground: 270 91% 98%;
    --muted: 270 91% 15%;
    --muted-foreground: 270 91% 65%;
    --accent: 270 91% 15%;
    --accent-foreground: 270 91% 98%;
    --border: 270 91% 15%;
    --input: 270 91% 15%;
    --ring: 270 91% 56%;
  }

  /* Turuncu teması - Aydınlık */
  [data-theme="orange"] {
    --background: 0 0% 100%;
    --foreground: 24 95% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 24 95% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 24 95% 4.9%;
    --primary: 24 95% 48%;
    --primary-foreground: 0 0% 100%;
    --secondary: 24 95% 95%;
    --secondary-foreground: 24 95% 20%;
    --muted: 24 95% 95%;
    --muted-foreground: 24 95% 40%;
    --accent: 24 95% 95%;
    --accent-foreground: 24 95% 20%;
    --border: 24 95% 90%;
    --input: 24 95% 90%;
    --ring: 24 95% 48%;
  }

  /* Turuncu teması - Koyu */
  [data-theme="orange-dark"] {
    --background: 24 95% 5%;
    --foreground: 24 95% 98%;
    --card: 24 95% 5%;
    --card-foreground: 24 95% 98%;
    --popover: 24 95% 5%;
    --popover-foreground: 24 95% 98%;
    --primary: 24 95% 48%;
    --primary-foreground: 0 0% 100%;
    --secondary: 24 95% 15%;
    --secondary-foreground: 24 95% 98%;
    --muted: 24 95% 15%;
    --muted-foreground: 24 95% 65%;
    --accent: 24 95% 15%;
    --accent-foreground: 24 95% 98%;
    --border: 24 95% 15%;
    --input: 24 95% 15%;
    --ring: 24 95% 48%;
  }

  /* Mavi teması - Aydınlık */
  [data-theme="blue"] {
    --background: 0 0% 100%;
    --foreground: 221 83% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 221 83% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 221 83% 4.9%;
    --primary: 221 83% 53%;
    --primary-foreground: 0 0% 100%;
    --secondary: 221 83% 95%;
    --secondary-foreground: 221 83% 20%;
    --muted: 221 83% 95%;
    --muted-foreground: 221 83% 40%;
    --accent: 221 83% 95%;
    --accent-foreground: 221 83% 20%;
    --border: 221 83% 90%;
    --input: 221 83% 90%;
    --ring: 221 83% 53%;
  }

  /* Mavi teması - Koyu */
  [data-theme="blue-dark"] {
    --background: 221 83% 5%;
    --foreground: 221 83% 98%;
    --card: 221 83% 5%;
    --card-foreground: 221 83% 98%;
    --popover: 221 83% 5%;
    --popover-foreground: 221 83% 98%;
    --primary: 221 83% 53%;
    --primary-foreground: 0 0% 100%;
    --secondary: 221 83% 15%;
    --secondary-foreground: 221 83% 98%;
    --muted: 221 83% 15%;
    --muted-foreground: 221 83% 65%;
    --accent: 221 83% 15%;
    --accent-foreground: 221 83% 98%;
    --border: 221 83% 15%;
    --input: 221 83% 15%;
    --ring: 221 83% 53%;
  }

  /* Siyah teması - Aydınlık */
  [data-theme="black"] {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
  }

  /* Siyah teması - Koyu */
  [data-theme="black-dark"] {
    --background: 0 0% 0%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground antialiased;
  }

  /* Modern scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/20 rounded-full hover:bg-muted-foreground/40 transition-colors;
  }

  /* Glass effect */
  .glass {
    @apply bg-background/80 backdrop-blur-md border shadow-sm;
  }
 
  /* Modern card hover effect */
  .card-hover {
    @apply hover:shadow-lg hover:-translate-y-0.5 transition-all duration-200;
  }

  /* Gradient text */
  .gradient-text {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-600;
  }

  /* Grid background */
  .bg-grid {
    background-size: 50px 50px;
    background-image: 
      linear-gradient(to right, rgb(var(--foreground) / 0.03) 1px, transparent 1px),
      linear-gradient(to bottom, rgb(var(--foreground) / 0.03) 1px, transparent 1px);
  }

  /* Modern container padding */
  @media (max-width: 640px) {
    .container {
      @apply px-4;
    }
  }

  /* Tema geçiş animasyonu */
  .theme-transition,
  .theme-transition *,
  .theme-transition *:before,
  .theme-transition *:after {
    transition: all 0.1s ease-in-out !important;
    transition-delay: 0 !important;
  }
}
