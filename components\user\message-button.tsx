"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/hooks/useAuth';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { MessageSquare } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { useMessages } from '@/lib/hooks/useMessages';

interface MessageButtonProps {
  userId: string;
  username: string;
}

export function MessageButton({ userId, username }: MessageButtonProps) {
  const { user } = useAuth();
  const router = useRouter();
  const { toast } = useToast();
  const { sendMessage } = useMessages(user?.uid || '');
  const [open, setOpen] = useState(false);
  const [message, setMessage] = useState('');
  const [sending, setSending] = useState(false);

  // Don't show button if user is not logged in or viewing their own profile
  if (!user || user.uid === userId) {
    return null;
  }

  const handleSendMessage = async () => {
    if (!message.trim()) return;

    setSending(true);
    try {
      await sendMessage(userId, message.trim());
      toast({
        title: 'Mesaj gönderildi',
        description: `@${username} kullanıcısına mesajınız iletildi.`,
      });
      setOpen(false);
      router.push('/messages');
    } catch (error) {
      toast({
        title: 'Hata',
        description: 'Mesaj gönderilirken bir hata oluştu.',
        variant: 'destructive',
      });
    } finally {
      setSending(false);
      setMessage('');
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="ml-4"
        >
          <MessageSquare className="w-4 h-4" />
          <span className="hidden sm:inline ml-2">Mesaj Gönder</span>
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Yeni Mesaj</DialogTitle>
          <DialogDescription className="text-muted-foreground">
            @{username} kullanıcısına mesaj gönder
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <Textarea
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            maxLength={500}
            placeholder="Mesajınızı yazın..."
            className="min-h-[100px]"
          />
          <p className="text-xs text-muted-foreground mt-2 text-right">
            {message.length}/500
          </p>
        </div>
        <DialogFooter>
          <Button
            onClick={handleSendMessage}
            disabled={sending || !message.trim()}
            className="w-full"
          >
            {sending ? (
              <span className="flex items-center">
                <span className="animate-spin mr-2">⏳</span>
                Gönderiliyor...
              </span>
            ) : (
              <>
                <MessageSquare className="w-4 h-4 mr-2" />
                Gönder
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}