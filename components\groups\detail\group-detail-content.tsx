"use client";

import { useAuth } from "@/lib/hooks/useAuth";
import { GroupHeader } from "./group-header";
import { GroupMembers } from "./group-members";
import { GroupRequests } from "./group-requests";
import { GroupInfo } from "./group-info";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import type { Group } from "@/lib/types/firebase";
import { Card } from "@/components/ui/card";

interface GroupDetailContentProps {
  group: Group;
}

export function GroupDetailContent({ group }: GroupDetailContentProps) {
  const { user } = useAuth();
  const isAdmin = user ? group.admins.includes(user.uid) : false;
  const isMember = user ? group.members.includes(user.uid) : false;

  return (
    <div className="container max-w-6xl mx-auto py-8 px-4">
      <GroupHeader group={group} />

      <Tabs defaultValue="info" className="mt-8">
        <TabsList>
          <TabsTrigger value="info">Grup Mesajları</TabsTrigger>
          {isAdmin && (
            <>
              <TabsTrigger value="members">Üyeler</TabsTrigger>
              <TabsTrigger value="requests">
                Katılım İstekleri
                {group.pendingRequests?.length > 0 && (
                <span className="ml-2 bg-primary text-primary-foreground rounded-full px-2 py-0.5 text-xs">
                  {group.pendingRequests.length}
                </span>
              )}
            </TabsTrigger>
            </>
          )}
        </TabsList>

        <TabsContent value="info" className="mt-6">
          {isMember ? (
            <GroupInfo group={group} />
          ) : (
            <Card className="p-6">
            <p className="text-center text-muted-foreground">
              Üye listesini görmek için gruba katılmanız gerekiyor.
            </p>
          </Card>
          )}
        </TabsContent>

        <TabsContent value="members" className="mt-6">
          <GroupMembers
            group={group}
            isAdmin={isAdmin}
            isMember={isMember}
          />
        </TabsContent>

        {isAdmin && (
          <TabsContent value="requests" className="mt-6">
            <GroupRequests group={group} />
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}