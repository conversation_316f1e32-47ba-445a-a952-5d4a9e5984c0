"use client";

import { useAuth } from '@/lib/hooks/useAuth';
import { ChatList } from '@/components/chat/chat-list';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function MessagesPage() {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/sign-in');
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!user) return null;

  return (
    <main className="container mx-auto max-w-6xl py-8 px-4">
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Mesajlar</h1>
          <p className="text-muted-foreground mt-2">
            <PERSON><PERSON><PERSON> oyuncularla olan mesajlaşmalarınız
          </p>
        </div>

        <ChatList />
      </div>
    </main>
  );
}