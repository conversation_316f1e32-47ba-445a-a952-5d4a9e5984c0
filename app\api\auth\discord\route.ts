import { NextResponse } from 'next/server';

export async function GET() {
  const clientId = process.env.DISCORD_CLIENT_ID;
  const redirectUri = `${process.env.NEXT_PUBLIC_BASE_URL}/api/auth/discord/callback`;
  const scope = encodeURIComponent('identify email');

  const discordAuthUrl = `https://discord.com/api/oauth2/authorize?` + 
    `client_id=${clientId}&` +
    `redirect_uri=${encodeURIComponent(redirectUri)}&` +
    `response_type=code&` +
    `scope=${scope}`;

  return NextResponse.redirect(discordAuthUrl);
} 