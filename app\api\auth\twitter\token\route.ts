import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const { code, state } = await request.json();
    // console.log('Received code and state:', { code, state }); // Debug için

    const tokenResponse = await fetch('https://api.twitter.com/2/oauth2/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Authorization: `Basic ${Buffer.from(
          `${process.env.NEXT_PUBLIC_TWITTER_CLIENT_ID}:${process.env.TWITTER_CLIENT_SECRET}`
        ).toString('base64')}`,
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        code,
        redirect_uri: `${process.env.NEXT_PUBLIC_BASE_URL}/api/auth/twitter/callback`,
        code_verifier: 'challenge',
      }),
    });

    // console.log('Token response status:', tokenResponse.status); // Debug için

    if (!tokenResponse.ok) {
      const errorData = await tokenResponse.text();
      console.error('Twitter token error:', errorData); // Debug için
      throw new Error(`Failed to fetch token from Twitter: ${errorData}`);
    }

    const tokenData = await tokenResponse.json();
    // console.log('Token data:', tokenData); // Debug için

    // Kullanıcı bilgilerini al
    const userResponse = await fetch('https://api.twitter.com/2/users/me?user.fields=profile_image_url', {
      headers: {
        Authorization: `Bearer ${tokenData.access_token}`,
      },
    });

    if (!userResponse.ok) {
      throw new Error('Failed to fetch user data from Twitter');
    }

    const userData = await userResponse.json();
    // console.log('User data:', userData); // Debug için

    return NextResponse.json({
      access_token: tokenData.access_token,
      user_id: userData.data.id,
      screen_name: userData.data.username,
      profile_image_url: userData.data.profile_image_url,
    });
  } catch (error) {
    console.error('Twitter token error:', error);
    return NextResponse.json(
      { error: 'Failed to exchange code for token' },
      { status: 500 }
    );
  }
}
