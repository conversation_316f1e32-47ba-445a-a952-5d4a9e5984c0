"use client";

import { useState, useEffect } from "react";
import { useTheme } from "next-themes";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Paintbrush2, Save, Trash2 } from "lucide-react";
import { convertToHSL } from "@/lib/utils";
import { useToast } from "@/components/ui/use-toast";

interface SavedTheme {
  id: string;
  name: string;
  colors: {
    primary: string;
    background: string;
    foreground: string;
  };
}

export function CustomThemeCreator() {
  const { setTheme } = useTheme();
  const { toast } = useToast();
  const [themeName, setThemeName] = useState("");
  const [savedThemes, setSavedThemes] = useState<SavedTheme[]>([]);
  const [customColors, setCustomColors] = useState({
    primary: "#4f46e5",
    background: "#ffffff",
    foreground: "#000000",
  });

  // Kayıtlı temaları yükle
  useEffect(() => {
    const themes = localStorage.getItem('savedThemes');
    if (themes) {
      setSavedThemes(JSON.parse(themes));
    }
  }, []);

  // Temaları kaydet
  const updateLocalStorage = (themes: SavedTheme[]) => {
    localStorage.setItem('savedThemes', JSON.stringify(themes));
    setSavedThemes(themes);
  };

  const handleColorChange = (color: string, type: keyof typeof customColors) => {
    setCustomColors(prev => ({ ...prev, [type]: color }));
  };

  const createCustomTheme = () => {
    const style = document.createElement('style');
    
    style.textContent = `
      [data-theme="custom"] {
        --background: ${convertToHSL(customColors.background).h} ${convertToHSL(customColors.background).s}% ${convertToHSL(customColors.background).l}%;
        --foreground: ${convertToHSL(customColors.foreground).h} ${convertToHSL(customColors.foreground).s}% ${convertToHSL(customColors.foreground).l}%;
        --primary: ${convertToHSL(customColors.primary).h} ${convertToHSL(customColors.primary).s}% ${convertToHSL(customColors.primary).l}%;
      }
    `;

    const oldStyle = document.querySelector('[data-custom-theme]');
    if (oldStyle) oldStyle.remove();

    style.setAttribute('data-custom-theme', '');
    document.head.appendChild(style);
    setTheme('custom');
  };

  const saveTheme = () => {
    if (!themeName.trim()) {
      toast({
        title: "Hata",
        description: "Lütfen tema için bir isim girin",
        variant: "destructive"
      });
      return;
    }

    const newTheme = {
      id: Date.now().toString(),
      name: themeName,
      colors: customColors
    };

    updateLocalStorage([...savedThemes, newTheme]);
    setThemeName("");
    
    toast({
      title: "Başarılı",
      description: "Tema kaydedildi"
    });
  };

  const deleteTheme = (themeId: string) => {
    updateLocalStorage(savedThemes.filter(t => t.id !== themeId));
    toast({
      title: "Başarılı",
      description: "Tema silindi"
    });
  };

  return (
    <Card className="p-6 mt-4">
      <div className="flex items-center gap-2 mb-4">
        <Paintbrush2 className="w-5 h-5" />
        <h3 className="text-lg font-medium">Özel Tema Oluştur</h3>
      </div>
      
      <div className="grid gap-4">
        <div className="grid gap-2">
          <Label>Ana Renk</Label>
          <div className="flex gap-2">
            <Input
              type="color"
              value={customColors.primary}
              onChange={(e) => handleColorChange(e.target.value, 'primary')}
              className="w-12 h-12 p-1 rounded-lg"
            />
            <Input
              type="text"
              value={customColors.primary}
              onChange={(e) => handleColorChange(e.target.value, 'primary')}
              className="font-mono"
            />
          </div>
        </div>

        <div className="grid gap-2">
          <Label>Arka Plan</Label>
          <div className="flex gap-2">
            <Input
              type="color"
              value={customColors.background}
              onChange={(e) => handleColorChange(e.target.value, 'background')}
              className="w-12 h-12 p-1 rounded-lg"
            />
            <Input
              type="text"
              value={customColors.background}
              onChange={(e) => handleColorChange(e.target.value, 'background')}
              className="font-mono"
            />
          </div>
        </div>

        <div className="grid gap-2">
          <Label>Yazı Rengi</Label>
          <div className="flex gap-2">
            <Input
              type="color"
              value={customColors.foreground}
              onChange={(e) => handleColorChange(e.target.value, 'foreground')}
              className="w-12 h-12 p-1 rounded-lg"
            />
            <Input
              type="text"
              value={customColors.foreground}
              onChange={(e) => handleColorChange(e.target.value, 'foreground')}
              className="font-mono"
            />
          </div>
        </div>

        <Button onClick={createCustomTheme} className="mt-2">
          Temayı Uygula
        </Button>

        <div 
          className="p-4 rounded-lg border mt-4"
          style={{
            backgroundColor: customColors.background,
            color: customColors.foreground
          }}
        >
          <div className="space-y-2">
            <h4 style={{ color: customColors.primary }}>Önizleme</h4>
            <p>Bu bir örnek metindir</p>
            <Button style={{ backgroundColor: customColors.primary }}>
              Örnek Buton
            </Button>
          </div>
        </div>
      </div>

      <div className="grid gap-4 mt-6">
        <div className="flex gap-2">
          <Input
            placeholder="Tema adı"
            value={themeName}
            onChange={(e) => setThemeName(e.target.value)}
          />
          <Button onClick={saveTheme} className="flex items-center gap-2">
            <Save className="w-4 h-4" />
            Kaydet
          </Button>
        </div>

        {savedThemes.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-medium">Kayıtlı Temalar</h4>
            <div className="grid gap-2">
              {savedThemes.map((theme) => (
                <div
                  key={theme.id}
                  className="flex items-center justify-between p-2 border rounded-lg"
                >
                  <div className="flex items-center gap-2">
                    <div className="flex gap-1">
                      {Object.values(theme.colors).map((color, i) => (
                        <div
                          key={i}
                          className="w-4 h-4 rounded-full border"
                          style={{ backgroundColor: color }}
                        />
                      ))}
                    </div>
                    <span>{theme.name}</span>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setCustomColors(theme.colors);
                        createCustomTheme();
                      }}
                    >
                      Uygula
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => deleteTheme(theme.id)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </Card>
  );
} 