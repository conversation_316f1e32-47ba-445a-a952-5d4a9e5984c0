"use client";

import { useState } from 'react';
import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  arrayUnion,
  arrayRemove,
  increment,
  getDoc,
  serverTimestamp,
  setDoc
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { Group } from '@/lib/types/firebase';

export function useGroupActions() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createGroup = async (groupData: Omit<Group, 'id' | 'createdAt' | 'memberCount'>, userId: string) => {
    setLoading(true);
    setError(null);

    try {
      const newGroup = await addDoc(collection(db, 'groups'), {
        ...groupData,
        memberCount: 1,
        createdAt: serverTimestamp(),
        members: [userId],
        admins: [userId],
        pendingRequests: []
      });

      // Update user's groups array
      await updateDoc(doc(db, 'users', userId), {
        groups: arrayUnion(newGroup.id)
      });

      return newGroup.id;
    } catch (err) {
      setError('Grup oluşturulurken bir hata oluştu.');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const joinGroup = async (groupId: string, userId: string) => {
    setLoading(true);
    setError(null);

    try {
      const groupRef = doc(db, 'groups', groupId);
      const groupDoc = await getDoc(groupRef);

      if (!groupDoc.exists()) {
        throw new Error('Grup bulunamadı.');
      }

      const groupData = groupDoc.data() as Group;

      // Check if user is already a member
      if (groupData.members.includes(userId)) {
        throw new Error('Zaten bu grubun üyesisiniz.');
      }

      // Check if user already has a pending request
      if (groupData.pendingRequests?.includes(userId)) {
        throw new Error('Zaten bekleyen bir katılım isteğiniz var.');
      }

      if (groupData.type === 'private') {
        // Add user to pending requests for private groups
        await updateDoc(groupRef, {
          pendingRequests: arrayUnion(userId)
        });

        // Add pending request to user's document
        await updateDoc(doc(db, 'users', userId), {
          pendingRequests: arrayUnion(groupId)
        });

        return 'pending';
      } else {
        // Direct join for public groups
        await updateDoc(groupRef, {
          members: arrayUnion(userId),
          memberCount: increment(1)
        });

        await updateDoc(doc(db, 'users', userId), {
          groups: arrayUnion(groupId)
        });

        return 'joined';
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Gruba katılırken bir hata oluştu.');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const leaveGroup = async (groupId: string, userId: string) => {
    setLoading(true);
    setError(null);

    try {
      const groupRef = doc(db, 'groups', groupId);
      const groupDoc = await getDoc(groupRef);

      if (!groupDoc.exists()) {
        throw new Error('Grup bulunamadı.');
      }

      const groupData = groupDoc.data() as Group;

      // Check if user is a member
      if (!groupData.members.includes(userId)) {
        throw new Error('Bu grubun üyesi değilsiniz.');
      }

      // Check if user is the last admin
      if (groupData.admins.includes(userId) && groupData.admins.length === 1) {
        throw new Error('Son yönetici olduğunuz için gruptan ayrılamazsınız.');
      }

      // Remove user from group
      await updateDoc(groupRef, {
        members: arrayRemove(userId),
        admins: arrayRemove(userId),
        memberCount: increment(-1)
      });

      // Remove group from user's groups
      await updateDoc(doc(db, 'users', userId), {
        groups: arrayRemove(groupId)
      });

      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Gruptan ayrılırken bir hata oluştu.');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const cancelJoinRequest = async (groupId: string, userId: string) => {
    setLoading(true);
    setError(null);

    try {
      const groupRef = doc(db, 'groups', groupId);
      const groupDoc = await getDoc(groupRef);

      if (!groupDoc.exists()) {
        throw new Error('Grup bulunamadı.');
      }

      // Remove pending request
      await updateDoc(groupRef, {
        pendingRequests: arrayRemove(userId)
      });

      // Remove from user's pending requests
      await updateDoc(doc(db, 'users', userId), {
        pendingRequests: arrayRemove(groupId)
      });

      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'İstek iptal edilirken bir hata oluştu.');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const toggleGroupStatus = async (groupId: string, isActive: boolean) => {
    try {
      const groupRef = doc(db, 'groups', groupId);
      await updateDoc(groupRef, {
        isActive: isActive,
        lastUpdated: serverTimestamp(),
      });

      // Bildirim gönder
      const notificationRef = doc(collection(db, 'groupNotifications'));
      await setDoc(notificationRef, {
        groupId,
        type: isActive ? 'GROUP_REOPENED' : 'GROUP_CLOSED',
        message: isActive ? 'Grup yeniden aktif edildi.' : 'Grup kapatıldı.',
        timestamp: serverTimestamp(),
      });

      return true;
    } catch (error) {
      console.error('Error toggling group status:', error);
      throw error;
    }
  };

  const closeGroup = async (groupId: string) => {
    try {
      const response = await fetch(`/api/groups/${groupId}/close`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Grup kapatılırken bir hata oluştu');
      }

      return true;
    } catch (error) {
      console.error('Grup kapatma hatası:', error);
      throw error;
    }
  };

  return {
    createGroup,
    joinGroup,
    leaveGroup,
    cancelJoinRequest,
    toggleGroupStatus,
    closeGroup,
    loading,
    error
  };
}