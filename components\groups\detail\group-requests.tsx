"use client";

import { useState, useEffect } from "react";
import { doc, getDoc, updateDoc, arrayUnion, arrayRemove, increment } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { useToast } from "@/components/ui/use-toast";
import type { Group, User } from "@/lib/types/firebase";

interface GroupRequestsProps {
  group: Group;
}

export function GroupRequests({ group }: GroupRequestsProps) {
  const [requests, setRequests] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [processingIds, setProcessingIds] = useState<string[]>([]);
  const { toast } = useToast();

  useEffect(() => {
    async function fetchRequests() {
      try {
        const requestPromises = (group.pendingRequests || []).map(async (userId: string) => {
          const userDoc = await getDoc(doc(db, "users", userId));
          return { id: userDoc.id, ...userDoc.data() } as User;
        });

        const requestData = await Promise.all(requestPromises);
        setRequests(requestData);
      } catch (error) {
        console.error("Error fetching requests:", error);
      } finally {
        setLoading(false);
      }
    }

    fetchRequests();
  }, [group.pendingRequests]);

  const handleRequest = async (userId: string, accept: boolean) => {
    setProcessingIds((prev) => [...prev, userId]);

    try {
      const groupRef = doc(db, "groups", group.id);
      const userRef = doc(db, "users", userId);

      const batch = [
        // Remove from pending requests
        updateDoc(groupRef, {
          pendingRequests: arrayRemove(userId)
        }),
        // Remove from user's pending requests
        updateDoc(userRef, {
          pendingRequests: arrayRemove(group.id)
        })
      ];

      if (accept) {
        // Add to members if accepted
        batch.push(
          updateDoc(groupRef, {
            members: arrayUnion(userId),
            memberCount: increment(1)
          }),
          updateDoc(userRef, {
            groups: arrayUnion(group.id)
          })
        );
      }

      await Promise.all(batch);

      setRequests((prev) => prev.filter((request) => request.id !== userId));

      toast({
        title: accept ? "Üye kabul edildi" : "İstek reddedildi",
        description: accept
          ? "Kullanıcı gruba başarıyla eklendi."
          : "Kullanıcının isteği reddedildi.",
      });
    } catch (error) {
      console.error("Error processing request:", error);
      toast({
        title: "Hata",
        description: "İstek işlenirken bir hata oluştu.",
        variant: "destructive",
      });
    } finally {
      setProcessingIds((prev) => prev.filter((id) => id !== userId));
    }
  };

  if (loading) {
    return (
      <Card className="p-6">
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="flex items-center gap-4">
              <div className="w-10 h-10 rounded-full bg-muted animate-pulse" />
              <div className="flex-1">
                <div className="h-4 bg-muted rounded w-1/4 animate-pulse" />
                <div className="h-3 bg-muted rounded w-1/3 mt-2 animate-pulse" />
              </div>
            </div>
          ))}
        </div>
      </Card>
    );
  }

  if (requests.length === 0) {
    return (
      <Card className="p-6">
        <p className="text-center text-muted-foreground">
          Bekleyen katılım isteği bulunmuyor.
        </p>
      </Card>
    );
  }

  return (
    <Card className="p-6">
      <div className="space-y-4">
        {requests.map((request) => (
          <div key={request.id} className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Avatar>
                <AvatarImage src={request.photoURL || ''} />
                <AvatarFallback>{request.nickname?.[0]}</AvatarFallback>
              </Avatar>
              <div>
                <span className="font-medium">{request.nickname}</span>
                <p className="text-sm text-muted-foreground">@{request.username}</p>
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleRequest(request.id as string, false)}
                disabled={processingIds.includes(request.id as string)}
              >
                Reddet
              </Button>
              <Button
                size="sm"
                onClick={() => handleRequest(request.id as string, true)}
                disabled={processingIds.includes(request.id as string)}
              >
                Kabul Et
              </Button>
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
}