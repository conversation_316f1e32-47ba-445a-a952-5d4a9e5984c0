"use client";

import { useState, useEffect, useMemo } from 'react';
import { db } from '@/lib/firebase';
import { collection, query, limit, onSnapshot, where, orderBy } from 'firebase/firestore';
import type { Group } from '@/lib/types/firebase';

interface UseGroupsOptions {
  limitCount?: number;
  searchQuery?: string;
  filters?: {
    categories?: string[];
    platforms?: string[];
  };
}

export function useGroups({ limitCount = 20, searchQuery, filters }: UseGroupsOptions) {
  const [data, setData] = useState<Group[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Query'i memoize et
  const queryRef = useMemo(() => {
    let q = query(
      collection(db, 'groups'),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );

    if (filters?.categories?.length) {
      q = query(q, where('category', 'in', filters.categories));
    }
    if (filters?.platforms?.length) {
      q = query(q, where('platform', 'in', filters.platforms));
    }

    return q;
  }, [limitCount, filters?.categories?.join(','), filters?.platforms?.join(',')]);

  // Filtrelenmiş grupları memoize et
  const filteredGroups = useMemo(() => {
    if (!searchQuery) return data;
    
    return data.filter(group => 
      group.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      group.description.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [data, searchQuery]);

  useEffect(() => {
    setIsLoading(true);

    const unsubscribe = onSnapshot(queryRef, 
      (snapshot) => {
        const groups = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Group[];

        setData(groups);
        setIsLoading(false);
      },
      (err) => {
        console.error('Error fetching groups:', err);
        setError(err as Error);
        setIsLoading(false);
      }
    );

    return () => unsubscribe();
  }, [queryRef]); // Sadece queryRef değiştiğinde effect'i çalıştır

  return { 
    data: filteredGroups, 
    isLoading, 
    error 
  };
}