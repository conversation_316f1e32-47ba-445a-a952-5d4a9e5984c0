'use client';

import Link from 'next/link';
import Image from 'next/image';
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Users } from "lucide-react";
import { Join<PERSON>roupButton } from './join-group-button';
import { Group } from '@/lib/types/firebase';

interface GroupCardProps {
  group: Group;
}

export function GroupCard({ group }: GroupCardProps) {
  const getGroupImage = (group: Group) => {
    if (group.image) return group.image;
    
    const categoryImages: Record<string, string> = {
      'fps': '/images/categories/fps.webp',
      'moba': '/images/categories/moba.webp',
      'mmorpg': '/images/categories/mmorpg.webp',
      'battle-royale': '/images/categories/battle-royale.webp',
      'strategy': '/images/categories/strategy.webp',
      'default': '/images/categories/default.webp'
    };
    
    return categoryImages[group.category.toLowerCase()] || categoryImages.default;
  };

  return (
    <Link href={`/groups/${group.id}`}>
      <Card className="overflow-hidden hover:shadow-lg transition-shadow">
        <div className="aspect-video relative">
          <Image
            src={getGroupImage(group)}
            alt={group.name}
            fill
            className="object-cover"
          />
        </div>
        <div className="p-4">
          <div className="flex items-start justify-between gap-2">
            <div className="space-y-1">
              <h3 className="font-semibold line-clamp-1">{group.name}</h3>
              <p className="text-sm text-muted-foreground line-clamp-2">
                {group.description}
              </p>
            </div>
            <div onClick={(e) => e.preventDefault()}>
              <JoinGroupButton groupId={group.id} type={group.type} />
            </div>
          </div>
          
          <div className="flex items-center gap-2 flex-wrap mt-3">
            <span className="text-sm flex items-center text-muted-foreground">
              <Users className="h-4 w-4 mr-1" />
              {group.memberCount} üye
            </span>
            <Badge variant="secondary">{group.category}</Badge>
            <Badge variant="outline">{group.platform}</Badge>
          </div>
        </div>
      </Card>
    </Link>
  );
} 