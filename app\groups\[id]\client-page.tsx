"use client";

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { checkGroupAccess } from '@/lib/firebase/group';
import { useAuth } from '@/lib/hooks/useAuth';
import { toast } from 'sonner';
import { GroupDetailWrapper } from '@/components/groups/detail/group-detail-wrapper';
import Loading from '@/app/loading';

export default function GroupPageClient() {
  const params = useParams();
  const groupId = typeof params?.id === 'string' ? params.id : '';
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (authLoading) return;

    const checkAccess = async () => {
      const hasAccess = await checkGroupAccess(groupId, user?.uid || null);
      
      if (!hasAccess) {
        toast.error('Bu gruba erişim yetkiniz yok');
        router.push('/groups');
        return;
      }
      
      setLoading(false);
    };

    checkAccess();
  }, [groupId, user, router, authLoading]);

  if (loading || authLoading) {
    return <Loading />;
  }

  return <GroupDetailWrapper groupId={groupId} />;
} 