import { cache } from 'react';
import { QueryClient } from '@tanstack/react-query';
import { adminService } from '@/lib/services/admin';

export const getQueryClient = cache(() => new QueryClient());

export async function prefetchQueries() {
  const queryClient = getQueryClient();

  // Admin dashboard verileri
  await queryClient.prefetchQuery({
    queryKey: ['admin-activity'],
    queryFn: () => adminService.getActivityData(7),
  });

  // Diğer prefetch işlemleri buraya eklenebilir
  // await queryClient.prefetchQuery({ ... });

  return queryClient;
} 