"use client";

import './globals.css';
import { metadata } from './metadata';
import { Inter } from 'next/font/google';
import { LayoutProvider } from '@/components/layout-provider';
import { Toaster } from 'sonner';
import { ThemeProvider } from "@/components/theme-provider";
import { useEffect, useState } from 'react';
import { getSettingsFromFirebase } from '@/lib/firebase';

const inter = Inter({ subsets: ['latin'] });

interface Settings {
  siteName: string;
  // Add other properties as needed
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [settings, setSettings] = useState<Settings | null>(null);

  useEffect(() => {
    const fetchSettings = async () => {
      const fetchedSettings = await getSettingsFromFirebase();
      if (fetchedSettings) {
        setSettings(fetchedSettings as Settings);
      }
    };

    fetchSettings();
  }, []);

  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {settings ? <title>{settings.siteName}</title> : null}
      </head>
      <body>
        <ThemeProvider
          attribute="data-theme"
          defaultTheme="blue"
          enableSystem={false}
          themes={[
            "black", "black-dark",
            "rose", "rose-dark",
            "green", "green-dark",
            "purple", "purple-dark",
            "orange", "orange-dark",
            "blue", "blue-dark"
          ]}
        >
          <LayoutProvider settings={settings}>
            {children}
          </LayoutProvider>
        </ThemeProvider>
        <Toaster richColors position="bottom-right" />
      </body>
    </html>
  );
}