import { db, auth } from "@/lib/firebase";
import { doc, getDoc, updateDoc, deleteDoc } from "firebase/firestore";
import { sendPasswordResetEmail } from "firebase/auth";
import { User } from "@/lib/types/user";

export const getUserById = async (userId: string) => {
  try {
    const userDoc = await getDoc(doc(db, "users", userId));
    if (!userDoc.exists()) return null;

    const data = userDoc.data();
    return {
      id: userDoc.id,
      ...data,
      createdAt: data.createdAt || "",
      lastLogin: data.lastLogin?.toDate?.() || data.lastLogin,
      bannedAt: data.bannedAt || ""
    };
  } catch (error) {
    console.error("Error getting user:", error);
    return null;
  }
};

export async function banUser(userId: string): Promise<void> {
  try {
    await updateDoc(doc(db, "users", userId), {
      banned: true,
      bannedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error banning user:', error);
    throw error;
  }
}

export async function unbanUser(userId: string): Promise<void> {
  try {
    await updateDoc(doc(db, "users", userId), {
      banned: false,
      bannedAt: null
    });
  } catch (error) {
    console.error('Error unbanning user:', error);
    throw error;
  }
}

export async function resetPassword(email: string): Promise<void> {
  try {
    await sendPasswordResetEmail(auth, email);
  } catch (error) {
    console.error('Error resetting password:', error);
    throw error;
  }
}

export async function deleteUser(userId: string): Promise<void> {
  try {
    await deleteDoc(doc(db, "users", userId));
  } catch (error) {
    console.error('Error deleting user:', error);
    throw error;
  }
} 