"use client";

import { useState, useEffect } from "react";
import { collection, query, where, getDocs, orderBy } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { Card } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import Link from "next/link";
import { Search } from "lucide-react";
import { Timestamp } from "firebase/firestore";
import { getPublicUsers, type PublicUser } from '@/lib/firebase/user';

export default function UsersPage() {
  const [users, setUsers] = useState<PublicUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState("newest");

  useEffect(() => {
    async function fetchPublicUsers() {
      try {
        const publicUsers = await getPublicUsers();
        setUsers(publicUsers);
      } catch (error) {
        console.error("Error fetching public users:", error);
      } finally {
        setLoading(false);
      }
    }

    fetchPublicUsers();
  }, []);

  const getSortedUsers = (users: PublicUser[]) => {
    const sortedUsers = [...users];
    
    switch (sortBy) {
      case "newest":
        return sortedUsers.sort((a, b) => 
          (b.createdAt?.toMillis() || 0) - (a.createdAt?.toMillis() || 0)
        );
      case "oldest":
        return sortedUsers.sort((a, b) => 
          (a.createdAt?.toMillis() || 0) - (b.createdAt?.toMillis() || 0)
        );
      case "az":
        return sortedUsers.sort((a, b) => {
          const nameA = (a.nickname || a.username).toLowerCase();
          const nameB = (b.nickname || b.username).toLowerCase();
          return nameA.localeCompare(nameB);
        });
      case "za":
        return sortedUsers.sort((a, b) => {
          const nameA = (a.nickname || a.username).toLowerCase();
          const nameB = (b.nickname || b.username).toLowerCase();
          return nameB.localeCompare(nameA);
        });
      default:
        return sortedUsers;
    }
  };

  const filteredAndSortedUsers = getSortedUsers(
    users.filter(user => {
      if (!user?.username) return false;
      
      const usernameMatch = user.username.toLowerCase().includes(searchQuery.toLowerCase());
      const nicknameMatch = user.nickname?.toLowerCase()?.includes(searchQuery.toLowerCase());
      
      return usernameMatch || nicknameMatch;
    })
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-6">Topluluk Üyeleri</h1>
        
        <div className="flex flex-col sm:flex-row gap-4 mb-8">
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3">
              <Search className="h-4 w-4 text-muted-foreground" />
            </div>
            <Input
              type="text"
              placeholder="Üye ara..."
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sıralama" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="newest">En Yeni Üyeler</SelectItem>
              <SelectItem value="oldest">En Eski Üyeler</SelectItem>
              <SelectItem value="az">A-Z</SelectItem>
              <SelectItem value="za">Z-A</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredAndSortedUsers.map((user) => (
          <Link href={`/users/${user.username}`} key={user.uid}>
            <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer flex flex-col items-center text-center">
              <Avatar className="w-24 h-24 mb-4">
                <AvatarImage
                  src={user.photoURL || `https://api.dicebear.com/7.x/avataaars/svg?seed=${user.uid}`}
                  alt={user.nickname || user.username}
                />
                <AvatarFallback>
                  {(user.nickname || user.username)?.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div>
                <h3 className="text-lg font-semibold mb-2">{user.nickname || user.username}</h3>
                {user.nickname && (
                  <p className="text-sm text-muted-foreground">@{user.username}</p>
                )}
              </div>
            </Card>
          </Link>
        ))}
      </div>

      {filteredAndSortedUsers.length === 0 && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">
            Aradığınız kriterlere uygun kullanıcı bulunamadı.
          </p>
        </div>
      )}
    </main>
  );
}