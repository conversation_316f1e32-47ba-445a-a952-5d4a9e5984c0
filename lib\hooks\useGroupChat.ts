import { useState, useEffect } from 'react';
import { useAuth } from './useAuth';
import { subscribeToGroupMessages, sendMessage, ChatMessage } from '@/lib/firebase/chat';
import { getDoc, doc } from 'firebase/firestore';
import { db } from '@/lib/firebase';

export function useGroupChat(groupId: string) {
  const { user } = useAuth();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    if (!user) return;

    setLoading(true);
    const unsubscribe = subscribeToGroupMessages(
      groupId,
      (newMessages) => {
        setMessages(newMessages);
        setLoading(false);
      },
      (error) => {
        setError(error);
        setLoading(false);
      }
    );

    return () => unsubscribe();
  }, [groupId, user]);

  const sendNewMessage = async (content: string | { content: string; senderPhoto?: string }) => {
    if (!user?.uid || !user.displayName) {
      throw new Error('Kullanıcı oturum açmamış');
    }

    const messageContent = typeof content === 'string' ? content : content.content;
    const photo = typeof content === 'string' ? undefined : content.senderPhoto;

    try {
      await sendMessage(groupId, messageContent, user.uid, user.displayName, photo);
      return true;
    } catch (error) {
      throw error;
    }
  };

  return {
    messages,
    loading,
    error,
    sendMessage: sendNewMessage
  };
} 