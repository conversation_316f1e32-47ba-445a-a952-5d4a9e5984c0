'use client';

import Link from 'next/link'; // Import Link
import { useParams } from 'next/navigation';
import { useState, useEffect } from 'react';
import { getUserById } from '@/lib/firebase/users';
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ban, UserX, KeyRound, Trash2, MailCheck, MailWarning } from "lucide-react"; // Added MailCheck, MailWarning
import { banUser, unbanUser, resetPassword, deleteUser } from '@/lib/firebase/users';
import { FirestoreUser, User } from '../../../types/user';

// Define GroupMembership type
interface GroupMembership {
  id: string;
  name: string;
  role?: string;
}

import Loading from '@/app/loading';
import { useToast } from "@/hooks/use-toast";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";

export default function UserDetailsPage() {
  const params = useParams();
  const userId = typeof params?.userId === 'string' ? params.userId : '';
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isBanned, setIsBanned] = useState(false);
  const [isEditing, setIsEditing] = useState(false); // Added isEditing state
  const [editableUserDetails, setEditableUserDetails] = useState<Partial<User>>({}); // Added editableUserDetails state
  const [userGroups, setUserGroups] = useState<GroupMembership[]>([]); // Added userGroups state
  const { success, error } = useToast();

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const userData = await getUserById(userId as string) as FirestoreUser;
        if (!userData) {
          setUser(null);
          return;
        }

        const processedData: User = {
          ...userData,
          email: userData.email || '',
          username: userData.username || '',
          createdAt: parseFirebaseDate(userData.createdAt),
          lastLogin: parseFirebaseDate(userData.lastLogin),
          bannedAt: parseFirebaseDate(userData.bannedAt),
          status: userData.status || 'active',
          role: userData.role || 'user'
        };
        
        setUser(processedData);
        setIsBanned(processedData.banned || false);

        // Simulate fetching user group memberships
        if (userId === "testUserIdWithGroups") { // Example condition
          setUserGroups([
            { id: "group1", name: "Awesome Gamers", role: "admin" },
            { id: "group2", name: "Strategy Experts", role: "member" },
          ]);
        } else {
          setUserGroups([]); // Default to empty or fetch actual data
        }

      } catch (error) {
        console.error('Kullanıcı bilgileri yüklenirken hata:', error);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [userId]);

  if (loading) {
    return <Loading />;
  }

  if (!user) {
    return <div className="p-4">Kullanıcı bulunamadı.</div>;
  }

  const handleEditToggle = () => {
    if (!isEditing) {
      setEditableUserDetails({
        nickname: user?.nickname || '',
        username: user?.username || '',
        // email: user?.email || '', // Email editing deferred
      });
    }
    setIsEditing(!isEditing);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setEditableUserDetails(prev => ({ ...prev, [name]: value }));
  };

  const handleSaveChanges = async () => {
    if (!user) return;
    try {
      // Placeholder for actual backend call:
      // await updateUserDetailsBackend(user.id, editableUserDetails);
      console.log("Simulating update user details for:", user.id, editableUserDetails);

      setUser(prevUser => {
        if (!prevUser) return null;
        const updatedDetails: User = {
          ...prevUser,
          ...editableUserDetails,
          id: prevUser.id,
          email: prevUser.email,
          role: prevUser.role,
          status: prevUser.status,
        };
        return updatedDetails;
      });
      setIsEditing(false);
      success("Kullanıcı detayları güncellendi. (Simulated)");
    } catch (err) {
      console.error("Error updating user details:", err);
      error("Kullanıcı detayları güncellenirken bir hata oluştu.");
    }
  };

  const handleMarkAsVerified = async () => {
    if (!user) return;
    const emailVerified = user.emailVerified || false;
    if (emailVerified) {
      success("Kullanıcının e-postası zaten doğrulanmış.");
      return;
    }
    try {
      console.log(`Simulating marking email as verified for user: ${user.id}`);
      setUser(prev => prev ? ({ ...prev, emailVerified: true }) : null);
      success("Kullanıcının e-postası doğrulandı olarak işaretlendi. (Simulated)");
    } catch (err) {
      console.error("Error marking email as verified:", err);
      error("E-posta doğrulanmış olarak işaretlenirken bir hata oluştu.");
    }
  };

  const handleResendVerificationEmail = async () => {
    if (!user || !user.email) return;
    const emailVerified = user.emailVerified || false;
    if (emailVerified) {
      success("Kullanıcının e-postası zaten doğrulanmış, yeniden göndermeye gerek yok.");
      return;
    }
    try {
      console.log(`Simulating resending verification email to: ${user.email}`);
      success(`Doğrulama e-postası ${user.email} adresine yeniden gönderildi. (Simulated)`);
    } catch (err) {
      console.error("Error resending verification email:", err);
      error("Doğrulama e-postası gönderilirken bir hata oluştu.");
    }
  };

  const handleBanToggle = async () => {
    try {
      if (isBanned) {
        await unbanUser(user.id);
        success('Kullanıcının yasağı kaldırıldı');
      } else {
        await banUser(user.id);
        success('Kullanıcı başarıyla yasaklandı');
      }
      setIsBanned(!isBanned);
    } catch (err) {
      console.error('İşlem sırasında hata:', err);
      error('Bir hata oluştu');
    }
  };

  const handleResetPassword = async () => {
    try {
      await resetPassword(user.email);
      success('Şifre sıfırlama bağlantısı gönderildi');
    } catch (err) {
      console.error('Şifre sıfırlanırken hata:', err);
      error('Şifre sıfırlama işlemi başarısız oldu');
    }
  };

  const handleDeleteUser = async () => {
    try {
      await deleteUser(user.id);
      success('Kullanıcı başarıyla silindi');
    } catch (err) {
      console.error('Kullanıcı silinirken hata:', err);
      error('Kullanıcı silme işlemi başarısız oldu');
    }
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Kullanıcı Detayları</h1>
        {!isEditing && (
          <Button onClick={handleEditToggle}>Düzenle</Button>
        )}
      </div>
      
      <Card className="p-6">
        {isEditing ? (
          <div className="space-y-4">
            <div>
              <Label htmlFor="nickname">Takma Ad</Label>
              <Input id="nickname" name="nickname" value={editableUserDetails.nickname || ''} onChange={handleInputChange} />
            </div>
            <div>
              <Label htmlFor="username">Kullanıcı Adı</Label>
              <Input id="username" name="username" value={editableUserDetails.username || ''} onChange={handleInputChange} />
            </div>
            {/* Display non-editable fields for context during editing */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <div><p className="font-semibold">Email:</p><p className="text-muted-foreground">{user.email}</p></div>
              <div><p className="font-semibold">Kayıt Tarihi:</p><p className="text-muted-foreground">{user.createdAt ? new Date(user.createdAt).toLocaleDateString('tr-TR') : '-'}</p></div>
              <div><p className="font-semibold">Son Giriş:</p><p className="text-muted-foreground">{user.lastLogin ? new Date(user.lastLogin).toLocaleDateString('tr-TR') : '-'}</p></div>
              <div><p className="font-semibold">Rol:</p><p className="text-muted-foreground">{user.role}</p></div>
              <div><p className="font-semibold">Durum:</p><p className="text-muted-foreground">{isBanned ? 'Yasaklı' : (user.status || 'Aktif')}</p></div>
              {isBanned && user.banReason && <div><p className="font-semibold">Yasaklama Nedeni:</p><p className="text-muted-foreground">{user.banReason}</p></div>}
            </div>
            <div className="flex justify-end gap-2 mt-6">
              <Button variant="outline" onClick={handleEditToggle}>İptal</Button>
              <Button onClick={handleSaveChanges}>Kaydet</Button>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div><p className="font-semibold">Kullanıcı ID:</p><p className="text-muted-foreground">{user.id}</p></div>
            <div><p className="font-semibold">Email:</p><p className="text-muted-foreground">{user.email}</p></div>
            <div><p className="font-semibold">Takma Ad:</p><p className="text-muted-foreground">{user.nickname || '-'}</p></div>
            <div><p className="font-semibold">Kullanıcı Adı:</p><p className="text-muted-foreground">{user.username || '-'}</p></div>
            <div><p className="font-semibold">Kayıt Tarihi:</p><p className="text-muted-foreground">{user.createdAt ? new Date(user.createdAt).toLocaleDateString('tr-TR') : '-'}</p></div>
            <div><p className="font-semibold">Son Giriş:</p><p className="text-muted-foreground">{user.lastLogin ? new Date(user.lastLogin).toLocaleDateString('tr-TR') : '-'}</p></div>
            <div><p className="font-semibold">Rol:</p><p className="text-muted-foreground">{user.role}</p></div>
            <div><p className="font-semibold">Durum:</p><p className="text-muted-foreground">{isBanned ? 'Yasaklı' : (user.status || 'Aktif')}</p></div>
            {isBanned && user.banReason && <div><p className="font-semibold">Yasaklama Nedeni:</p><p className="text-muted-foreground">{user.banReason}</p></div>}
          </div>
        )}

        {/* Admin actions are always visible below the user details or edit form */}
        <div className="mt-8 space-y-4">
          <h2 className="text-xl font-semibold">Admin İşlemleri</h2>
          <div className="flex flex-wrap gap-4">
            <Button onClick={handleBanToggle} variant="destructive">
              <Ban className="w-4 h-4 mr-2" />
              {isBanned ? 'Yasağı Kaldır' : 'Kullanıcıyı Yasakla'}
            </Button>
            <Button onClick={handleMarkAsVerified} variant="outline" disabled={user?.emailVerified || false}>
              <MailCheck className="w-4 h-4 mr-2" />
              {user?.emailVerified ? "E-posta Doğrulanmış" : "E-postayı Doğrula"}
            </Button>
            <Button onClick={handleResendVerificationEmail} variant="outline" disabled={user?.emailVerified || false}>
              <MailWarning className="w-4 h-4 mr-2" />
              Doğrulama E-postasını Yeniden Gönder
            </Button>
            <Button onClick={handleResetPassword} variant="outline">
              <KeyRound className="w-4 h-4 mr-2" />
              Şifre Sıfırla
            </Button>

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive">
                  <Trash2 className="w-4 h-4 mr-2" />
                  Kullanıcıyı Sil
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Kullanıcıyı Sil</AlertDialogTitle>
                  <AlertDialogDescription>
                    Bu kullanıcıyı silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>İptal</AlertDialogCancel>
                  <AlertDialogAction onClick={handleDeleteUser}>
                    Sil
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </Card>

      {/* User Group Memberships Card */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold mb-4">Grup Üyelikleri ({userGroups.length})</h2>
        {userGroups.length > 0 ? (
          <ul className="space-y-3">
            {userGroups.map(group => (
              <li key={group.id} className="flex justify-between items-center p-3 border rounded-md hover:bg-muted/50">
                <div>
                  <Link href={`/admin/groups/${group.id}`} className="font-medium text-primary hover:underline">
                    {group.name}
                  </Link>
                  {group.role && (
                    <span className="ml-2 text-sm text-muted-foreground">({group.role})</span>
                  )}
                </div>
                <Button variant="outline" size="sm" asChild>
                  <Link href={`/admin/groups/${group.id}`}>Grubu Görüntüle</Link>
                </Button>
              </li>
            ))}
          </ul>
        ) : (
          <p className="text-muted-foreground">Kullanıcı herhangi bir gruba üye değil.</p>
        )}
      </Card>
    </div>
  );
} 

// Timestamp veya ISO string formatındaki tarihleri Date objesine çevirir
const parseFirebaseDate = (date: any): Date | null => {
  if (!date) return null;
  
  // Eğer Timestamp objesi ise
  if (date.seconds) {
    return new Date(date.seconds * 1000);
  }
  
  // Eğer ISO string ise
  if (typeof date === 'string') {
    return new Date(date);
  }
  
  return null;
}; 