"use client";

import { useGroups } from "@/lib/hooks/useGroups";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Users, Sparkles } from "lucide-react";

export function NewGroups() {
  const { groups, loading } = useGroups({ limitCount:10 })

  if (loading) {
    return <div className="space-y-4">
      {[...Array(3)].map((_, i) => (
        <Card key={i} className="p-6 animate-pulse">
          <div className="h-4 bg-muted rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-muted rounded w-3/4"></div>
        </Card>
      ))}
    </div>;
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Sparkles className="h-5 w-5 text-primary" />
        <h2 className="text-2xl font-semibold"><PERSON><PERSON></h2>
      </div>

      <div className="space-y-4">
        {groups.map((group) => (
          <Card key={group.id} className="p-6">
            <div className="flex items-start justify-between">
              <div className="space-y-1">
                <h3 className="font-semibold">{group.name}</h3>
                <p className="text-sm text-muted-foreground">{group.description}</p>
                <div className="flex items-center space-x-4 mt-2">
                  <span className="text-sm flex items-center">
                    <Users className="h-4 w-4 mr-1" />
                    {group.memberCount} üye
                  </span>
                  <span className="text-sm">{group.category}</span>
                </div>
              </div>
              <Button>Katıl</Button>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
}