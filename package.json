{"name": "oyuncu-bul", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "set NODE_ENV=production&& node server.js", "lint": "next lint", "socket-server": "tsx server/standalone-socket-server.ts"}, "dependencies": {"@genkit-ai/googleai": "^0.9.12", "@hookform/resolvers": "^3.3.4", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.1.4", "@tanstack/react-query": "^5.62.7", "@types/node": "^20.11.17", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@types/react-syntax-highlighter": "^15.5.13", "@types/socket.io": "^3.0.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "crypto-browserify": "^3.12.1", "date-fns": "^3.3.1", "firebase": "^11.0.2", "genkit": "^0.9.12", "lucide-react": "^0.330.0", "mediasoup": "^3.15.2", "mediasoup-client": "^3.7.18", "next": "^15.1.0", "next-auth": "^4.24.11", "next-themes": "^0.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.50.1", "react-icons": "^5.4.0", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.6.1", "react-tooltip": "^5.28.0", "recharts": "^2.12.0", "shadcn-ui": "^0.9.4", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^1.7.0", "stream-browserify": "^3.0.0", "string-replace-loader": "^3.1.0", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "undici": "^6.21.0", "zod": "^3.22.4", "zustand": "^5.0.2"}, "devDependencies": {"@types/openid": "^2.0.5", "@types/passport-steam": "^1.0.6", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "tsx": "^4.19.2", "typescript": "^5.3.3"}}