"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/lib/hooks/useAuth";
import { useGroupActions } from "@/lib/hooks/useGroupActions";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Loader2 } from "lucide-react";
import { doc, getDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";

interface JoinGroupButtonProps {
  groupId: string;
  type: 'public' | 'private';
}

export function JoinGroupButton({ groupId, type }: JoinGroupButtonProps) {
  const { user } = useAuth();
  const { joinGroup, leaveGroup, cancelJoinRequest, loading } = useGroupActions();
  const { toast } = useToast();
  const [status, setStatus] = useState<'idle' | 'pending' | 'joined'>('idle');

  useEffect(() => {
    async function checkStatus() {
      if (!user) return;

      try {
        const groupDoc = await getDoc(doc(db, 'groups', groupId));
        const groupData = groupDoc.data();

        if (groupData?.members.includes(user.uid)) {
          setStatus('joined');
        } else if (groupData?.pendingRequests?.includes(user.uid)) {
          setStatus('pending');
        } else {
          setStatus('idle');
        }
      } catch (error) {
        console.error('Error checking group status:', error);
      }
    }

    checkStatus();
  }, [groupId, user]);

  const handleJoin = async () => {
    if (!user) {
      toast({
        title: "Giriş yapmalısınız",
        description: "Gruba katılmak için önce giriş yapmalısınız.",
        variant: "destructive",
      });
      return;
    }

    try {
      const result = await joinGroup(groupId, user.uid);
      setStatus(result);
      
      toast({
        title: result === 'pending' ? "Katılım isteği gönderildi" : "Gruba katıldınız",
        description: result === 'pending' 
          ? "Grup yöneticisi onayladıktan sonra katılabilirsiniz."
          : "Başarıyla gruba katıldınız!",
      });
    } catch (error) {
      toast({
        title: "Hata",
        description: error instanceof Error ? error.message : "Bir hata oluştu.",
        variant: "destructive",
      });
    }
  };

  const handleLeave = async () => {
    if (!user) return;

    try {
      await leaveGroup(groupId, user.uid);
      setStatus('idle');
      
      toast({
        title: "Gruptan ayrıldınız",
        description: "Başarıyla gruptan ayrıldınız.",
      });
    } catch (error) {
      toast({
        title: "Hata",
        description: error instanceof Error ? error.message : "Bir hata oluştu.",
        variant: "destructive",
      });
    }
  };

  const handleCancelRequest = async () => {
    if (!user) return;

    try {
      await cancelJoinRequest(groupId, user.uid);
      setStatus('idle');
      
      toast({
        title: "İstek iptal edildi",
        description: "Katılım isteğiniz iptal edildi.",
      });
    } catch (error) {
      toast({
        title: "Hata",
        description: error instanceof Error ? error.message : "Bir hata oluştu.",
        variant: "destructive",
      });
    }
  };

  if (status === 'joined') {
    return (
      <Button variant="outline" onClick={handleLeave} disabled={loading}>
        {loading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Ayrılınıyor...
          </>
        ) : (
          "Gruptan Ayrıl"
        )}
      </Button>
    );
  }

  if (status === 'pending') {
    return (
      <Button variant="outline" onClick={handleCancelRequest} disabled={loading}>
        {loading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            İptal ediliyor...
          </>
        ) : (
          "İsteği İptal Et"
        )}
      </Button>
    );
  }

  return (
    <Button onClick={handleJoin} disabled={loading}>
      {loading ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          {type === 'private' ? 'İstek Gönderiliyor...' : 'Katılınıyor...'}
        </>
      ) : (
        type === 'private' ? 'Katılım İsteği Gönder' : 'Katıl'
      )}
    </Button>
  );
}