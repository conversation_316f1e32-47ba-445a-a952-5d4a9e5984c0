"use client";

import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search, AlertTriangle, Plus } from "lucide-react";

export default function AlertsPage() {
  const [loading, setLoading] = useState(false);
  const [showNewAlert, setShowNewAlert] = useState(false);
  const [filter, setFilter] = useState("all");
  const [alertType, setAlertType] = useState("");
  const [alertMessage, setAlertMessage] = useState("");

  const handleCreateAlert = async () => {
    if (!alertType || !alertMessage.trim()) return;

    setLoading(true);
    try {
      // Add alert creation logic here
      setShowNewAlert(false);
      setAlertType("");
      setAlertMessage("");
    } catch (error) {
      console.error("Error creating alert:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Uyarılar</h1>
        <Dialog open={showNewAlert} onOpenChange={setShowNewAlert}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Yeni Uyarı
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Yeni Uyarı Oluştur</DialogTitle>
              <DialogDescription>
                Sistem genelinde gösterilecek bir uyarı oluşturun.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Select value={alertType} onValueChange={setAlertType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Uyarı türü seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="info">Bilgi</SelectItem>
                    <SelectItem value="warning">Uyarı</SelectItem>
                    <SelectItem value="error">Hata</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Textarea
                  placeholder="Uyarı mesajı"
                  value={alertMessage}
                  onChange={(e) => setAlertMessage(e.target.value)}
                  className="min-h-[100px]"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowNewAlert(false)}>
                İptal
              </Button>
              <Button 
                onClick={handleCreateAlert}
                disabled={loading || !alertType || !alertMessage.trim()}
              >
                {loading ? "Oluşturuluyor..." : "Oluştur"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold">Aktif Uyarılar</h3>
              <p className="text-3xl font-bold mt-2">5</p>
            </div>
            <AlertTriangle className="h-8 w-8 text-yellow-500" />
          </div>
        </Card>

        <Card className="p-6">
          <div>
            <h3 className="font-semibold">Çözülen Uyarılar</h3>
            <p className="text-3xl font-bold mt-2">23</p>
          </div>
        </Card>

        <Card className="p-6">
          <div>
            <h3 className="font-semibold">Toplam Uyarılar</h3>
            <p className="text-3xl font-bold mt-2">28</p>
          </div>
        </Card>
      </div>

      <Card className="p-4">
        <div className="flex items-center gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Uyarı ara..."
              className="pl-10"
            />
          </div>
          <Select value={filter} onValueChange={setFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filtrele" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tümü</SelectItem>
              <SelectItem value="active">Aktif</SelectItem>
              <SelectItem value="resolved">Çözülen</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </Card>

      <div className="space-y-4">
        {[1, 2, 3].map((alert) => (
          <Card key={alert} className="p-6">
            <div className="flex items-start justify-between">
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <Badge variant="destructive">Önemli</Badge>
                  <span className="text-sm text-muted-foreground">
                    2 saat önce
                  </span>
                </div>
                <p className="font-medium">
                  Sistem bakımı nedeniyle bazı özellikler geçici olarak
                  kullanılamayabilir.
                </p>
              </div>
              <Button variant="outline" size="sm">
                Çözüldü
              </Button>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
}