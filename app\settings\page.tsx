"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/lib/hooks/useAuth";
import { useRouter } from "next/navigation";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/components/ui/use-toast";
import { doc, updateDoc, getDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { Shield, Lock, Paintbrush, Moon, Sun, Laptop, Paintbrush2 } from "lucide-react";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useTheme } from "next-themes";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ThemeSelector } from "@/components/theme/theme-selector";
import { CustomThemeCreator } from "@/components/theme/custom-theme-creator";

// Hex'i HSL'e çeviren yardımcı fonksiyon
function convertToHSL(hex: string) {
  // hex to rgb
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  if (!result) return { h: 0, s: 0, l: 0 };
  
  const r = parseInt(result[1], 16) / 255;
  const g = parseInt(result[2], 16) / 255;
  const b = parseInt(result[3], 16) / 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0, s, l = (max + min) / 2;

  if (max === min) {
    h = s = 0;
  } else {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }

  return {
    h: Math.round(h * 360),
    s: Math.round(s * 100),
    l: Math.round(l * 100)
  };
}

export default function SettingsPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const { toast } = useToast();
  const [isPublic, setIsPublic] = useState(true);

  useEffect(() => {
    if (!loading && !user) {
      router.push("/auth/sign-in");
    }
  }, [user, loading, router]);

  useEffect(() => {
    // Kullanıcı ayarlarını yükle
    const loadUserSettings = async () => {
      if (user) {
        const userDoc = await getDoc(doc(db, "users", user.uid));
        if (userDoc.exists()) {
          const userData = userDoc.data();
          setIsPublic(userData.isPublic ?? true);
        }
      }
    };

    loadUserSettings();
  }, [user]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  const handlePrivacyToggle = async () => {
    try {
      const newIsPublic = !isPublic;
      await updateDoc(doc(db, "users", user.uid), {
        isPublic: newIsPublic,
      });
      setIsPublic(newIsPublic);
      toast({
        title: "Gizlilik ayarı güncellendi",
        description: newIsPublic
          ? "Profiliniz artık herkese açık."
          : "Profiliniz artık gizli.",
      });
    } catch (error) {
      console.error("Error updating privacy settings:", error);
      toast({
        title: "Hata",
        description: "Gizlilik ayarları güncellenirken bir hata oluştu.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="container mx-auto py-10 px-4 sm:px-6 lg:px-8">
      <h1 className="text-2xl font-bold mb-6">Ayarlar</h1>
      
      <Tabs defaultValue="security" className="space-y-4">
        <TabsList>
          <TabsTrigger value="security" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Güvenlik
          </TabsTrigger>
          <TabsTrigger value="privacy" className="flex items-center gap-2">
            <Lock className="h-4 w-4" />
            Gizlilik
          </TabsTrigger>
          <TabsTrigger value="theme" className="flex items-center gap-2">
            <Paintbrush className="h-4 w-4" />
            Tema
          </TabsTrigger>
        </TabsList>

        <TabsContent value="security">
          <Card className="p-6">
            <h2 className="text-lg font-medium mb-4">Güvenlik Ayarları</h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <h3 className="text-base font-medium">İki Faktörlü Doğrulama</h3>
                  <p className="text-sm text-muted-foreground">
                    Hesabınızı daha güvenli hale getirmek için iki faktörlü doğrulamayı etkinleştirin
                  </p>
                </div>
                <Switch />
              </div>
              {/* Diğer güvenlik ayarları buraya eklenebilir */}
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="privacy">
          <Card className="p-6">
            <h2 className="text-lg font-medium mb-4">Gizlilik Ayarları</h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <h3 className="text-base font-medium">Profil Gizliliği</h3>
                  <p className="text-sm text-muted-foreground">
                    Profilinizin diğer kullanıcılar tarafından görüntülenebilmesine izin verin
                  </p>
                </div>
                <Switch
                  checked={isPublic}
                  onCheckedChange={handlePrivacyToggle}
                />
              </div>
              {/* Diğer gizlilik ayarları buraya eklenebilir */}
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="theme">
          <Card className="p-6">
            <h2 className="text-lg font-medium mb-4">Tercih ettiğiniz temayı seçin</h2>
            <div className="space-y-4">
              <ThemeSelector />
              <CustomThemeCreator />
            </div>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
} 