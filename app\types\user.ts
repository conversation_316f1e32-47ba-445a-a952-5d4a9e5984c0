import { Timestamp } from 'firebase/firestore';

export interface FirestoreUser {
  id: string;
  email: string;
  username: string;
  nickname?: string;
  photoURL?: string;
  createdAt: Timestamp | string | Date;
  lastLogin?: Timestamp | string | Date;
  bannedAt?: Timestamp | string | Date;
  status: 'active' | 'banned' | 'suspended';
  role: 'admin' | 'moderator' | 'user';
  banned?: boolean;
  banReason?: string;
}

export interface User extends Omit<FirestoreUser, 'createdAt' | 'lastLogin' | 'bannedAt'> {
  createdAt: Date | null;
  lastLogin?: Date | null;
  bannedAt?: Date | null;
} 