import { db } from '@/lib/firebase';
import { 
  collection, 
  addDoc, 
  query, 
  where, 
  orderBy, 
  getDocs, 
  serverTimestamp,
  doc,
  updateDoc,
  deleteDoc,
  limit,
  QueryConstraint
} from 'firebase/firestore';
import type { AdminLog, AdminPermission } from '@/lib/types/firebase';

interface CreateLogParams {
  adminId: string;
  action: string;
  targetType: AdminLog['targetType'];
  targetId: string;
  details?: Record<string, any>;
}

export const adminLogs = {
  // Log oluşturma
  async createLog({
    adminId,
    action,
    targetType,
    targetId,
    details
  }: CreateLogParams): Promise<string> {
    try {
      const logRef = await addDoc(collection(db, 'adminLogs'), {
        adminId,
        action,
        targetType,
        targetId,
        timestamp: serverTimestamp(),
        ip: '', // API'den alınacak
        userAgent: navigator.userAgent,
        status: 'success',
        details
      });

      return logRef.id;
    } catch (error) {
      console.error('Error creating admin log:', error);
      throw error;
    }
  },

  // Logları getirme
  async getLogs(filters?: {
    adminId?: string;
    targetType?: AdminLog['targetType'];
    startDate?: Date;
    endDate?: Date;
    limit?: number;
  }) {
    try {
      const logsRef = collection(db, 'adminLogs');
      const constraints: QueryConstraint[] = [];

      if (filters?.adminId) {
        constraints.push(where('adminId', '==', filters.adminId));
      }

      if (filters?.targetType) {
        constraints.push(where('targetType', '==', filters.targetType));
      }

      if (filters?.startDate) {
        constraints.push(where('timestamp', '>=', filters.startDate));
      }

      if (filters?.endDate) {
        constraints.push(where('timestamp', '<=', filters.endDate));
      }

      constraints.push(orderBy('timestamp', 'desc'));

      if (filters?.limit) {
        constraints.push(limit(filters.limit));
      }

      const q = query(logsRef, ...constraints);
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as AdminLog[];
    } catch (error) {
      console.error('Error fetching admin logs:', error);
      throw error;
    }
  }
};

// Admin işlemleri için yardımcı fonksiyon
export async function withAdminLog(
  adminId: string,
  action: string,
  targetType: AdminLog['targetType'],
  targetId: string,
  operation: () => Promise<any>,
  details?: Record<string, any>
) {
  try {
    // İşlemi gerçekleştir
    const result = await operation();

    // Başarılı log kaydı
    await adminLogs.createLog({
      adminId,
      action,
      targetType,
      targetId,
      details: {
        ...details,
        success: true
      }
    });

    return result;
  } catch (error) {
    // Hata durumunda log kaydı
    await adminLogs.createLog({
      adminId,
      action,
      targetType,
      targetId,
      details: {
        ...details,
        error: error instanceof Error ? error.message : 'Unknown error',
        success: false
      }
    });

    throw error;
  }
}

// Kullanım örneği:
export const adminOperations = {
  async banUser(adminId: string, userId: string, reason: string) {
    return withAdminLog(
      adminId,
      'BAN_USER',
      'user',
      userId,
      async () => {
        // Kullanıcı banlama işlemi
        const userRef = doc(db, 'users', userId);
        await updateDoc(userRef, {
          isBanned: true,
          bannedAt: serverTimestamp(),
          bannedBy: adminId,
          banReason: reason
        });
      },
      { reason }
    );
  },

  async deleteGroup(adminId: string, groupId: string, reason: string) {
    return withAdminLog(
      adminId,
      'DELETE_GROUP',
      'group',
      groupId,
      async () => {
        // Grup silme işlemi
        const groupRef = doc(db, 'groups', groupId);
        await deleteDoc(groupRef);
      },
      { reason }
    );
  }
}; 