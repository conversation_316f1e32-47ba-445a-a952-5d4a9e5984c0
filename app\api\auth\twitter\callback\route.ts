import { NextResponse } from 'next/server';
import { redirect } from 'next/navigation';

export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const code = url.searchParams.get('code');
    const state = url.searchParams.get('state');

    if (!code) {
      return Response.redirect(`${process.env.NEXT_PUBLIC_BASE_URL}/profile?error=no_code`);
    }

    return Response.redirect(`${process.env.NEXT_PUBLIC_BASE_URL}/profile?code=${code}&state=${state}`);
  } catch (error) {
    console.error('Twitter callback error:', error);
    return Response.redirect(`${process.env.NEXT_PUBLIC_BASE_URL}/profile?error=callback_failed`);
  }
}
