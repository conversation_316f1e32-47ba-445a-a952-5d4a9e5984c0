"use client";

import { useState, useEffect } from "react";
import Link from 'next/link'; // Import Link
import { collection, getDocs } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, Users, ListChecks, Edit, ToggleLeft, ToggleRight } from "lucide-react"; // Added Edit, ToggleLeft, ToggleRight
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from "@/components/ui/dropdown-menu"; // Added DropdownMenu components
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from "@/components/ui/alert-dialog"; // Added AlertDialog components
import { useToast } from "@/hooks/use-toast"; // Added useToast (assuming path)
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { formatDistanceToNow } from "date-fns";
import { tr } from "date-fns/locale";

export default function GroupsPage() {
  const [groups, setGroups] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedGroups, setSelectedGroups] = useState<string[]>([]); // Added selectedGroups state
  const { toast } = useToast(); // Added toast hook

  useEffect(() => {
    async function fetchGroups() {
      try {
        const groupsSnapshot = await getDocs(collection(db, "groups"));
        const groupsData = groupsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate(),
          isActive: doc.data().isActive ?? true
        }));
        setGroups(groupsData);
      } catch (error) {
        console.error("Error fetching groups:", error);
      } finally {
        setLoading(false);
      }
    }

    fetchGroups();
  }, []);

  const filteredGroups = groups.filter(group =>
    group.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    group.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getStatusBadge = (group: any) => {
    if (!group.isActive) {
      return (
        <Badge variant="destructive">
          Kapalı
        </Badge>
      );
    }

  const handleToggleGroupStatus = async (groupId: string, currentIsActive: boolean) => {
    setLoading(true);
    try {
      // Placeholder for actual backend call:
      // await backendToggleGroupStatus(groupId, !currentIsActive);
      console.log(`Simulating toggling status for group ${groupId} to ${!currentIsActive}`);

      setGroups(prevGroups =>
        prevGroups.map(g => (g.id === groupId ? { ...g, isActive: !currentIsActive } : g))
      );

      toast({
        title: "Durum Değiştirildi",
        description: `Grup durumu başarıyla güncellendi. (Simulated)`,
      });
    } catch (error) {
      console.error("Error toggling group status:", error);
      toast({ title: "Hata", description: "Grup durumu güncellenirken bir hata oluştu.", variant: "destructive" });
    } finally {
      setLoading(false);
    }
  };

    return (
      <Badge variant={group.type === 'public' ? 'default' : 'secondary'}>
        {group.type === 'public' ? 'Açık' : 'Özel'}
      </Badge>
    );
  };

  const handleSelectAllGroups = (checked: boolean | string) => {
    if (checked) {
      setSelectedGroups(filteredGroups.map(group => group.id));
    } else {
      setSelectedGroups([]);
    }
  };

  const handleSelectGroup = (groupId: string, checked: boolean | string) => {
    if (checked) {
      setSelectedGroups(prev => [...prev, groupId]);
    } else {
      setSelectedGroups(prev => prev.filter(id => id !== groupId));
    }
  };

  const handleBulkGroupAction = async (action: 'activate' | 'deactivate' | 'delete') => {
    if (selectedGroups.length === 0) {
      toast({ title: "Uyarı", description: "Lütfen işlem yapmak için en az bir grup seçin.", variant: "default" });
      return;
    }
    setLoading(true);
    try {
      for (const groupId of selectedGroups) {
        if (action === 'activate') {
          // TODO: await toggleGroupStatus(groupId, true);
          console.log(`Simulating activating group: ${groupId}`);
        } else if (action === 'deactivate') {
          // TODO: await toggleGroupStatus(groupId, false);
          console.log(`Simulating deactivating group: ${groupId}`);
        } else if (action === 'delete') {
          // TODO: await deleteGroupBackend(groupId);
          console.log(`Simulating deleting group: ${groupId}`);
        }
      }

      // Refetch groups or update local state
      const groupsSnapshot = await getDocs(collection(db, "groups"));
      const groupsData = groupsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate(),
        isActive: doc.data().isActive ?? true
      }));
      setGroups(groupsData);

      toast({ title: "Başarılı", description: `Seçilen gruplar üzerinde işlem (${action}) başarıyla gerçekleştirildi. (Simulated)` });
      setSelectedGroups([]);

    } catch (error) {
      console.error(`Error during bulk group action (${action}):`, error);
      toast({ title: "Hata", description: "Toplu işlem sırasında bir hata oluştu.", variant: "destructive" });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Gruplar</h1>
        <div className="flex items-center gap-2">
          {selectedGroups.length > 0 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <ListChecks className="w-4 h-4 mr-2" />
                  Toplu İşlem ({selectedGroups.length})
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Seçili Gruplar İçin</DropdownMenuLabel>
                <DropdownMenuItem onClick={() => handleBulkGroupAction("activate")}>Aktif Et</DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleBulkGroupAction("deactivate")}>Pasif Et</DropdownMenuItem>
                <DropdownMenuSeparator />
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <DropdownMenuItem className="text-red-600 hover:!bg-red-50 hover:!text-red-700" onSelect={(e) => e.preventDefault()}>Sil</DropdownMenuItem>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Grupları Sil</AlertDialogTitle>
                      <AlertDialogDescription>
                        {selectedGroups.length} grubu silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>İptal</AlertDialogCancel>
                      <AlertDialogAction className="bg-red-600 hover:bg-red-700" onClick={() => handleBulkGroupAction("delete")}>Sil</AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
          <Button> {/* This will be replaced by CreateGroupDialog in a later subtask */}
            <Users className="w-4 h-4 mr-2" />
            Yeni Grup
          </Button>
        </div>
      </div>

      <Card className="p-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Grup ara..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </Card>

      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={filteredGroups.length > 0 && selectedGroups.length === filteredGroups.length}
                  onCheckedChange={handleSelectAllGroups}
                  aria-label="Tümünü seç"
                />
              </TableHead>
              <TableHead>Grup Adı</TableHead>
              <TableHead>Kategori</TableHead>
              <TableHead>Platform</TableHead>
              <TableHead>Üye Sayısı</TableHead>
              <TableHead>Oluşturulma</TableHead>
              <TableHead>Durum</TableHead>
              <TableHead className="text-right">İşlemler</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center"> {/* Adjusted colSpan */}
                  <div className="flex items-center justify-center py-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                </TableCell>
              </TableRow>
            ) : filteredGroups.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-4"> {/* Adjusted colSpan */}
                  Grup bulunamadı
                </TableCell>
              </TableRow>
            ) : (
              filteredGroups.map((group) => (
                <TableRow key={group.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedGroups.includes(group.id)}
                      onCheckedChange={(checked) => handleSelectGroup(group.id, checked)}
                      aria-label={`Grup seç ${group.name}`}
                    />
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="font-medium">{group.name}</p>
                      <p className="text-sm text-muted-foreground truncate max-w-md">
                        {group.description}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary">{group.category}</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{group.platform}</Badge>
                  </TableCell>
                  <TableCell>{group.memberCount}</TableCell>
                  <TableCell>
                    {group.createdAt ? formatDistanceToNow(group.createdAt, { addSuffix: true, locale: tr }) : '-'}
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(group)}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end space-x-1">
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/admin/groups/${group.id}`}>Detaylar</Link>
                      </Button>
                      <Button variant="ghost" size="icon" className="h-8 w-8" asChild>
                        <Link href={`/admin/groups/${group.id}/edit`} title="Düzenle"> {/* Placeholder for edit route */}
                          <Edit className="h-4 w-4" />
                        </Link>
                      </Button>
                      <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => handleToggleGroupStatus(group.id, group.isActive)} title={group.isActive ? "Pasif Et" : "Aktif Et"}>
                        {group.isActive ? <ToggleRight className="h-4 w-4 text-green-500" /> : <ToggleLeft className="h-4 w-4 text-red-500" />}
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </Card>
    </div>
  );
}