import { db } from '@/lib/firebase';
import { onAuthStateChanged } from 'firebase/auth';
import { doc, getDoc, serverTimestamp, setDoc } from 'firebase/firestore';
import { auth } from './auth';

export const initializePresence = () => {
  onAuthStateChanged(auth, async (user) => {
    if (!user) return;

    // Her 3 dakikada bir lastLogin'i güncelle
    const updateLastLogin = async () => {
      try {
        const userRef = doc(db, 'users', user.uid);
        const userDoc = await getDoc(userRef);
        if (userDoc.exists()) {
          await setDoc(userRef, {
            lastLogin: serverTimestamp(),
          }, { merge: true });
        }
      } catch (error) {
        console.error('Last login update error:', error);
      }
    };

    // İlk giriş anında güncelle
    await updateLastLogin();

    // 3 dakikada bir güncelleme için interval başlat
    const interval = setInterval(updateLastLogin, 3 * 60 * 1000);

    // Cleanup function
    return () => clearInterval(interval);
  });
};