import { collection, query, where, getDocs, getDoc, doc } from 'firebase/firestore';
import { db, auth } from '@/lib/firebase';
import { signInWithEmailAndPassword } from 'firebase/auth';

export { auth };

export async function isUsernameTaken(username: string): Promise<boolean> {
  const q = query(
    collection(db, 'users'),
    where('username', '==', username.toLowerCase())
  );
  
  const querySnapshot = await getDocs(q);
  return !querySnapshot.empty;
}

export async function signIn(email: string, password: string) {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    
    // Kullanıcının yasaklı olup olmadığını kontrol et
    const userDoc = await getDoc(doc(db, 'users', userCredential.user.uid));
    const userData = userDoc.data();
    
    if (userData?.banned) {
      await auth.signOut();
      // Yasaklı kullanıcı için de "email/password wrong" hatası fırlat
      throw new Error('auth/wrong-password');
    }

    return userCredential;
  } catch (error: any) {
    // Firebase'in orijinal hatasını yeniden fırlat
    if (error?.code) {
      throw error;
    }
    // Bizim fırlattığımız hataları Firebase hatasına dönüştür
    throw { code: error.message };
  }
}