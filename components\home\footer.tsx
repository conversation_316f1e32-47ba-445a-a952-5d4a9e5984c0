import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { GamepadIcon } from 'lucide-react';

export function Footer() {
  return (
    <footer className="border-t">
      <div className="container mx-auto max-w-6xl px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <GamepadIcon className="w-6 h-6" />
              <span className="font-bold">Oyuncu Bul</span>
            </div>
            <p className="text-sm text-muted-foreground">
              Türkiye'nin en büyük oyuncu eşleştirme platformu
            </p>
          </div>

          <div>
            <h4 className="font-semibold mb-4">Platform</h4>
            <ul className="space-y-2">
              <li>
                <Button variant="link" className="h-auto p-0" asChild>
                  <Link href="/groups">Gruplar</Link>
                </Button>
              </li>
              <li>
                <Button variant="link" className="h-auto p-0" asChild>
                  <Link href="/auth/sign-up">Kayıt Ol</Link>
                </Button>
              </li>
              <li>
                <Button variant="link" className="h-auto p-0" asChild>
                  <Link href="/auth/sign-in">Giriş Yap</Link>
                </Button>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="font-semibold mb-4">Kaynaklar</h4>
            <ul className="space-y-2">
              <li>
                <Button variant="link" className="h-auto p-0" asChild>
                  <Link href="/blog">Blog</Link>
                </Button>
              </li>
              <li>
                <Button variant="link" className="h-auto p-0" asChild>
                  <Link href="/docs">Dokümantasyon</Link>
                </Button>
              </li>
              <li>
                <Button variant="link" className="h-auto p-0" asChild>
                  <Link href="/faq">SSS</Link>
                </Button>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="font-semibold mb-4">Yasal</h4>
            <ul className="space-y-2">
              <li>
                <Button variant="link" className="h-auto p-0" asChild>
                  <Link href="/privacy/">Gizlilik Politikası</Link>
                </Button>
              </li>
              <li>
                <Button variant="link" className="h-auto p-0" asChild>
                  <Link href="/terms/">Kullanım Koşulları</Link>
                </Button>
              </li>
              <li>
                <Button variant="link" className="h-auto p-0" asChild>
                  <Link href="/cookies/">Çerez Politikası</Link>
                </Button>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-12 pt-8 border-t text-center text-sm text-muted-foreground">
          <p>&copy; {new Date().getFullYear()} Oyuncu Bul. Tüm hakları saklıdır.</p>
        </div>
      </div>
    </footer>
  );
}