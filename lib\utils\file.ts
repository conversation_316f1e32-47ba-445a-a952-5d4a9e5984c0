import fs from 'fs/promises';
import path from 'path';

export async function saveGroupImage(groupId: string, file: File): Promise<string> {
  const bytes = await file.arrayBuffer();
  const buffer = Buffer.from(bytes);

  // Dosya uzantısını al
  const extension = file.name.split('.').pop()?.toLowerCase() || 'webp';
  
  // Dosya adını oluştur
  const fileName = `group-${groupId}.${extension}`;
  
  // Kayıt yolu
  const publicPath = path.join(process.cwd(), 'public', 'images', 'groups');
  const filePath = path.join(publicPath, fileName);

  // Klasör yoksa oluştur
  await fs.mkdir(publicPath, { recursive: true });
  
  // Dosyayı kaydet
  await fs.writeFile(filePath, buffer);

  // Public URL'i döndür
  return `/images/groups/${fileName}`;
} 