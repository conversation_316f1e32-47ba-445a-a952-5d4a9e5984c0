"use client";

import { useState } from "react";
import { useGroups } from "@/lib/hooks/useGroups";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Users, Grid } from "lucide-react";

export function AllGroups() {
  const [sortBy, setSortBy] = useState("memberCount");
  const { data: groups, isLoading } = useGroups({ limitCount: 20 });

  if (isLoading) {
    return <div className="space-y-4">
      {[...Array(5)].map((_, i) => (
        <Card key={i} className="p-6 animate-pulse">
          <div className="h-4 bg-muted rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-muted rounded w-3/4"></div>
        </Card>
      ))}
    </div>;
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Grid className="h-5 w-5 text-primary" />
          <h2 className="text-2xl font-semibold">Tüm Gruplar</h2>
        </div>

        <Select value={sortBy} onValueChange={setSortBy}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Sıralama" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="memberCount">Üye Sayısı</SelectItem>
            <SelectItem value="name">İsim</SelectItem>
            <SelectItem value="createdAt">Oluşturma Tarihi</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-4">
        {groups.map((group) => (
          <Card key={group.id} className="p-6">
            <div className="flex items-start justify-between">
              <div className="space-y-1">
                <h3 className="font-semibold">{group.name}</h3>
                <p className="text-sm text-muted-foreground">{group.description}</p>
                <div className="flex items-center space-x-4 mt-2">
                  <span className="text-sm flex items-center">
                    <Users className="h-4 w-4 mr-1" />
                    {group.memberCount} üye
                  </span>
                  <span className="text-sm">{group.category}</span>
                </div>
              </div>
              <Button>Katıl</Button>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
}