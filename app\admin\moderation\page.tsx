"use client";

import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search, Shield } from "lucide-react";

const reports = [
  {
    id: "1",
    type: "spam",
    reportedUser: "user123",
    reportedBy: "moderator1",
    content: "Spam içerik paylaşımı",
    status: "pending",
    date: new Date(),
  },
  {
    id: "2",
    type: "abuse",
    reportedUser: "user456",
    reportedBy: "moderator2",
    content: "Uygunsuz davranış",
    status: "resolved",
    date: new Date(),
  },
];

export default function ModerationPage() {
  const [filter, setFilter] = useState("all");
  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(false);

  const filteredReports = reports.filter(report => {
    if (filter !== "all" && report.status !== filter) return false;
    if (search) {
      const searchLower = search.toLowerCase();
      return (
        report.reportedUser.toLowerCase().includes(searchLower) ||
        report.content.toLowerCase().includes(searchLower)
      );
    }
    return true;
  });

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row items-center justify-between mb-2">
        <h1 className="text-3xl font-bold mb-2">Moderasyon</h1>
        <Button>
          <Shield className="w-4 h-4 mr-2" />
          Moderatör Ekle
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold">Bekleyen Raporlar</h3>
              <p className="text-3xl font-bold mt-2">12</p>
            </div>
            <Badge variant="destructive">Acil</Badge>
          </div>
        </Card>

        <Card className="p-6">
          <div>
            <h3 className="font-semibold">Çözülen Raporlar</h3>
            <p className="text-3xl font-bold mt-2">45</p>
          </div>
        </Card>

        <Card className="p-6">
          <div>
            <h3 className="font-semibold">Aktif Moderatörler</h3>
            <p className="text-3xl font-bold mt-2">8</p>
          </div>
        </Card>
      </div>

      <Card className="p-4">
        <div className="flex items-center gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Rapor ara..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={filter} onValueChange={setFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filtrele" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tümü</SelectItem>
              <SelectItem value="pending">Bekleyenler</SelectItem>
              <SelectItem value="resolved">Çözülenler</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </Card>

      <Card>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Tür</TableHead>
                <TableHead>Raporlanan</TableHead>
                <TableHead>Raporlayan</TableHead>
                <TableHead>İçerik</TableHead>
                <TableHead>Durum</TableHead>
                <TableHead className="text-right">İşlemler</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredReports.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-4">
                    Rapor bulunamadı
                  </TableCell>
                </TableRow>
              ) : (
                filteredReports.map((report) => (
                  <TableRow key={report.id}>
                    <TableCell>
                      <Badge variant="outline">{report.type}</Badge>
                    </TableCell>
                    <TableCell>{report.reportedUser}</TableCell>
                    <TableCell>{report.reportedBy}</TableCell>
                    <TableCell>{report.content}</TableCell>
                    <TableCell>
                      <Badge
                        variant={report.status === "pending" ? "destructive" : "secondary"}
                      >
                        {report.status === "pending" ? "Bekliyor" : "Çözüldü"}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="outline" size="sm">
                        İncele
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        )}
      </Card>
    </div>
  );
}