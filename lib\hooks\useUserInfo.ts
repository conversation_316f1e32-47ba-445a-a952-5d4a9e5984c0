import { useState, useEffect } from 'react';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';

interface UserInfo {
  nickname: string;
  username: string;
  photoURL: string | null;
}

export function useUserInfo(userId: string | undefined) {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    async function fetchUserInfo() {
      if (!userId) {
        setLoading(false);
        return;
      }

      try {
        const userDoc = await getDoc(doc(db, 'users', userId));
        if (userDoc.exists()) {
          const userData = userDoc.data();
          setUserInfo({
            nickname: userData.nickname || '<PERSON>oni<PERSON>llan<PERSON>',
            username: userData.username,
            photoURL: userData.photoURL
          });
        }
        setError(null);
      } catch (err) {
        console.error('Error fetching user info:', err);
        setError(err as Error);
      } finally {
        setLoading(false);
      }
    }

    fetchUserInfo();
  }, [userId]);

  return { userInfo, loading, error };
}