import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const { code } = await request.json();

    // console.log('Requesting token with:', {
    //   code,
    //   clientId: process.env.NEXT_PUBLIC_TWITCH_CLIENT_ID,
    //   redirectUri: `${process.env.NEXT_PUBLIC_BASE_URL}/api/auth/twitch/callback`
    // });

    const tokenResponse = await fetch('https://id.twitch.tv/oauth2/token', {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams({
        client_id: process.env.NEXT_PUBLIC_TWITCH_CLIENT_ID!,
        client_secret: process.env.TWITCH_CLIENT_SECRET!,
        code,
        grant_type: 'authorization_code',
        redirect_uri: `${process.env.NEXT_PUBLIC_BASE_URL}/api/auth/twitch/callback`,
      }),
    });

    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text();
      console.error('Twitch API Error:', {
        status: tokenResponse.status,
        statusText: tokenResponse.statusText,
        body: errorText
      });
      
      try {
        const errorData = JSON.parse(errorText);
        throw new Error(`Twitch API Error: ${JSON.stringify(errorData)}`);
      } catch {
        throw new Error(`Twitch API Error: ${errorText}`);
      }
    }

    const tokenData = await tokenResponse.json();
    // console.log('Token Response:', {
    //   status: tokenResponse.status,
    //   data: tokenData
    // });

    return NextResponse.json(tokenData);
  } catch (error: any) {
    console.error('Token error details:', {
      message: error.message,
      code: error.code,
      stack: error.stack
    });

    return NextResponse.json({ 
      error: error.message
    }, { status: 500 });
  }
} 