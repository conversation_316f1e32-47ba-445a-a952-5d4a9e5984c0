"use client";

import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { saveSettingsToFirebase, getSettingsFromFirebase } from '@/lib/firebase'; // Firebase'den ayarları kaydetmek ve almak için import
import { useState, useEffect } from 'react';
import { initialSettings } from "@/app/config/settings";
import { toast } from "@/components/ui/use-toast";
import { Toaster } from "@/components/ui/toaster";

export default function SettingsPage() {
  const [currentSettings, setCurrentSettings] = useState(initialSettings); // initialSettings'i kullanarak ayarları tanımlıyoruz
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setIsLoading(true);
        const settings = await getSettingsFromFirebase();
        if (settings) {
          setCurrentSettings(settings as typeof initialSettings);
        }
      } catch (error) {
        toast({
          title: "Hata",
          description: "Ayarlar yüklenirken bir hata oluştu.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchSettings();
  }, []);

  const handleSaveSettings = async () => {
    try {
      await saveSettingsToFirebase(currentSettings);
      toast({
        title: "Ayarlar kaydedildi",
        description: "Tüm değişiklikler başarıyla kaydedildi.",
      });
    } catch (error) {
      toast({
        title: "Hata",
        description: "Ayarlar kaydedilirken bir hata oluştu.",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return <div className="flex items-center justify-center h-screen">Yükleniyor...</div>;
  }

  return (
    <div className="space-y-6">
      <Toaster />
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Ayarlar</h1>
      </div>

      <Tabs defaultValue="general">
        <TabsList>
          <TabsTrigger value="general">Genel</TabsTrigger>
          <TabsTrigger value="security">Güvenlik</TabsTrigger>
          <TabsTrigger value="notifications">Bildirimler</TabsTrigger>
          <TabsTrigger value="api">API</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-6">
          <Card className="p-6">
            <h2 className="text-lg font-semibold mb-6">Site Ayarları</h2>
            <div className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="site-name">Site Adı</Label>
                <Input 
                  id="site-name" 
                  value={currentSettings.siteName} 
                  onChange={(e) => setCurrentSettings({ ...currentSettings, siteName: e.target.value })} 
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="site-description">Site Açıklaması</Label>
                <Input
                  id="site-description"
                  value={currentSettings.siteDescription} 
                  onChange={(e) => setCurrentSettings({ ...currentSettings, siteDescription: e.target.value })} 
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="maintenance-mode">Bakım Modu</Label>
                <div className="flex items-center space-x-2">
                  <Switch 
                    id="maintenance-mode"
                    checked={currentSettings.maintenanceMode} 
                    onCheckedChange={(checked) => setCurrentSettings({ ...currentSettings, maintenanceMode: checked })} 
                  />
                  <Label htmlFor="maintenance-mode">Aktif</Label>
                </div>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <h2 className="text-lg font-semibold mb-6">Kayıt Ayarları</h2>
            <div className="space-y-4">
              <div className="grid gap-2">
                <Label>Kayıt Sistemi</Label>
                <div className="flex items-center space-x-2">
                  <Switch 
                    id="registration" 
                    checked={currentSettings.registrationEnabled} 
                    onCheckedChange={(checked) => setCurrentSettings({ ...currentSettings, registrationEnabled: checked })} 
                  />
                  <Label htmlFor="registration">Kayıtlar Açık</Label>
                </div>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="verification">E-posta Doğrulama</Label>
                <Select 
                  value={currentSettings.emailVerification} 
                  onValueChange={(value) => setCurrentSettings({ ...currentSettings, emailVerification: value })} 
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Doğrulama türü seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="required">Zorunlu</SelectItem>
                    <SelectItem value="optional">İsteğe Bağlı</SelectItem>
                    <SelectItem value="disabled">Devre Dışı</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <Card className="p-6">
            <h2 className="text-lg font-semibold mb-6">Güvenlik Ayarları</h2>
            <div className="space-y-4">
              <div className="grid gap-2">
                <Label>İki Faktörlü Doğrulama</Label>
                <div className="flex items-center space-x-2">
                  <Switch id="2fa" />
                  <Label htmlFor="2fa">Zorunlu 2FA</Label>
                </div>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="session-timeout">Oturum Zaman Aşımı</Label>
                <Select defaultValue="24h">
                  <SelectTrigger id="session-timeout">
                    <SelectValue placeholder="Süre seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1h">1 Saat</SelectItem>
                    <SelectItem value="12h">12 Saat</SelectItem>
                    <SelectItem value="24h">24 Saat</SelectItem>
                    <SelectItem value="7d">7 Gün</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <h2 className="text-lg font-semibold mb-6">IP Güvenliği</h2>
            <div className="space-y-4">
              <div className="grid gap-2">
                <Label>IP Kısıtlaması</Label>
                <div className="flex items-center space-x-2">
                  <Switch id="ip-restriction" />
                  <Label htmlFor="ip-restriction">IP Kısıtlaması Aktif</Label>
                </div>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="allowed-ips">İzin Verilen IP'ler</Label>
                <Input
                  id="allowed-ips"
                  placeholder="IP adreslerini virgülle ayırarak girin"
                />
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-6">
          <Card className="p-6">
            <h2 className="text-lg font-semibold mb-6">Bildirim Ayarları</h2>
            <div className="space-y-4">
              <div className="grid gap-2">
                <Label>E-posta Bildirimleri</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Switch id="email-security" defaultChecked />
                    <Label htmlFor="email-security">Güvenlik Uyarıları</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch id="email-updates" defaultChecked />
                    <Label htmlFor="email-updates">Sistem Güncellemeleri</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch id="email-reports" />
                    <Label htmlFor="email-reports">Haftalık Raporlar</Label>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="api" className="space-y-6">
          <Card className="p-6">
            <h2 className="text-lg font-semibold mb-6">API Ayarları</h2>
            <div className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="api-key">API Anahtarı</Label>
                <div className="flex gap-2">
                  <Input
                    id="api-key"
                    value="sk_live_xxxxxxxxxxxxxxxxxxxxx"
                    readOnly
                  />
                  <Button variant="outline">Yenile</Button>
                </div>
              </div>

              <div className="grid gap-2">
                <Label>Webhook URL</Label>
                <Input placeholder="https://example.com/webhook" />
              </div>

              <div className="grid gap-2">
                <Label>Rate Limiting</Label>
                <Select defaultValue="5000">
                  <SelectTrigger>
                    <SelectValue placeholder="Limit seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1000">1,000 istek/saat</SelectItem>
                    <SelectItem value="5000">5,000 istek/saat</SelectItem>
                    <SelectItem value="10000">10,000 istek/saat</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="flex justify-end gap-4">
        <Button variant="outline">İptal</Button>
        <Button onClick={handleSaveSettings}>Değişiklikleri Kaydet</Button>
      </div>
    </div>
  )};