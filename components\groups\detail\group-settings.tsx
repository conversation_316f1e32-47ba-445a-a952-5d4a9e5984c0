"use client";

import { useState } from "react";
import { doc, updateDoc, deleteDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Settings, Trash2 } from "lucide-react";
import { useRouter } from "next/navigation";
import type { Group } from "@/lib/types/firebase";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";

interface GroupSettingsProps {
  group: Group;
}

export function GroupSettings({ group }: GroupSettingsProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [name, setName] = useState(group.name);
  const [description, setDescription] = useState(group.description);
  const { toast } = useToast();
  const router = useRouter();

  const handleUpdateBasicInfo = async () => {
    if (!name.trim()) return;
    
    setLoading(true);
    try {
      await updateDoc(doc(db, "groups", group.id), {
        name: name.trim(),
        description: description.trim(),
      });

      toast({
        title: "Grup güncellendi",
        description: "Grup bilgileri başarıyla güncellendi.",
      });
    } catch (error) {
      toast({
        title: "Hata",
        description: "Grup güncellenirken bir hata oluştu.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteGroup = async () => {
    setLoading(true);
    try {
      await deleteDoc(doc(db, "groups", group.id));
      toast({
        title: "Grup silindi",
        description: "Grup başarıyla silindi.",
      });
      router.push("/groups");
    } catch (error) {
      toast({
        title: "Hata",
        description: "Grup silinirken bir hata oluştu.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
      setOpen(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Settings className="w-4 h-4 mr-2" />
          Grup Ayarları
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Grup Ayarları</DialogTitle>
          <DialogDescription>
            Grup ayarlarını buradan yönetebilirsiniz.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Temel Bilgiler */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Temel Bilgiler</h3>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Grup Adı</Label>
                <Input
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Grup adı"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Açıklama</Label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Grup açıklaması"
                  rows={3}
                />
              </div>
              <Button 
                onClick={handleUpdateBasicInfo} 
                disabled={loading || !name.trim() || 
                  (name === group.name && description === group.description)}
              >
                {loading ? "Güncelleniyor..." : "Değişiklikleri Kaydet"}
              </Button>
            </div>
          </div>

          <Separator />

          {/* Silme İşlemi */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-destructive">Tehlikeli İşlemler</h3>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" className="w-full">
                  <Trash2 className="w-4 h-4 mr-2" />
                  Grubu Sil
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Grubu silmek istediğinize emin misiniz?</AlertDialogTitle>
                  <AlertDialogDescription>
                    Bu işlem geri alınamaz. Grup ve tüm içeriği kalıcı olarak silinecektir.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>İptal</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDeleteGroup}
                    disabled={loading}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  >
                    {loading ? "Siliniyor..." : "Grubu Sil"}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 