"use client";

import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import type { AdminPermission } from "@/lib/types/firebase";

const PERMISSIONS: { value: AdminPermission; label: string }[] = [
  { value: "users.view", label: "Kullanıcıları Görüntüleme" },
  { value: "users.manage", label: "Kullanıcı Yönetimi" },
  { value: "groups.view", label: "Grupları Görüntüleme" },
  { value: "groups.manage", label: "Grup Yönetimi" },
  { value: "reports.view", label: "Raporlar<PERSON> Görüntüleme" },
  { value: "reports.manage", label: "<PERSON>or Yönetimi" },
];

interface PermissionSelectProps {
  value: AdminPermission[];
  onChange: (value: AdminPermission[]) => void;
}

export function PermissionSelect({ value, onChange }: PermissionSelectProps) {
  const handleToggle = (permission: AdminPermission) => {
    if (value.includes(permission)) {
      onChange(value.filter((p) => p !== permission));
    } else {
      onChange([...value, permission]);
    }
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {PERMISSIONS.map((permission) => (
          <div key={permission.value} className="flex items-center space-x-2">
            <Checkbox
              id={permission.value}
              checked={value.includes(permission.value)}
              onCheckedChange={() => handleToggle(permission.value)}
            />
            <Label htmlFor={permission.value}>{permission.label}</Label>
          </div>
        ))}
      </div>
    </div>
  );
} 