const USERNAME_REGEX = /^[a-zA-Z0-9_]{3,20}$/;

export function isValidUsername(username: string): { valid: boolean; message?: string } {
  // Remove whitespace
  const trimmedUsername = username.trim();

  // Check length
  if (trimmedUsername.length < 3 || trimmedUsername.length > 20) {
    return {
      valid: false,
      message: "Kullanıcı adı 3-20 karakter arasında olmalıdır"
    };
  }

  // Check for valid characters
  if (!USERNAME_REGEX.test(trimmedUsername)) {
    return {
      valid: false,
      message: "<PERSON><PERSON><PERSON><PERSON><PERSON> adı sadece harf, rakam ve alt çizgi içerebilir"
    };
  }

  return { valid: true };
}