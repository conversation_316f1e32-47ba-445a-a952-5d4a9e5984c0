"use client";

import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  GamepadIcon, 
  Menu, 
  MessageCircle, 
  Users, 
  Info,
  Home,
  Calendar,
  ShoppingBag
} from "lucide-react";
import { ModeToggle } from "@/components/mode-toggle";
import { useMessageNotifications } from "@/lib/hooks/useMessageNotifications";
import { UserNav } from "@/components/user-nav";
import { MobileNav } from "@/components/mobile-nav";
import { useAuth } from "@/lib/hooks/useAuth";
import { Badge } from "@/components/ui/badge";
import { useUnreadMessages } from '@/hooks/use-unread-messages';
import { cn } from "@/lib/utils";

const mainNavItems = [
  {
    title: "Ana Sayfa",
    href: "/",
    icon: Home
  },
  {
    title: "Gruplar",
    href: "/groups",
    icon: Users
  },
  {
    title: "Üyeler",
    href: "/users",
    icon: Users
  },
  {
    title: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    href: "/events",
    icon: Calendar
  },
  {
    title: "<PERSON><PERSON><PERSON><PERSON>",
    href: "/store",
    icon: ShoppingBag
  }
];

export function Navbar() {
  const { user } = useAuth();
  const unreadCount = useUnreadMessages(user);
  useMessageNotifications(user?.uid);

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex justify-center w-full">
        <div className="w-full max-w-[2000px] px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            {/* Logo */}
            <div className="flex shrink-0 items-center">
              <Link 
                href="/" 
                className="flex items-center space-x-2"
              >
                <GamepadIcon className="h-6 w-6 text-primary" />
                <span className="font-bold text-lg hidden sm:inline-block">
                  Oyuncu Bul
                </span>
              </Link>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center justify-center flex-1 px-4">
              <div className="flex items-center justify-center space-x-1">
                {mainNavItems.map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      "px-3 py-2 text-sm font-medium rounded-md transition-colors hover:bg-muted",
                      "flex items-center gap-2",
                      "relative group"
                    )}
                  >
                    <item.icon className="h-4 w-4" />
                    <span className="hidden lg:inline-block">{item.title}</span>
                    {item.href === '/messages' && unreadCount > 0 && (
                      <span className="absolute -top-1 -right-1 flex h-3 w-3">
                        <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-destructive opacity-75"></span>
                        <span className="relative inline-flex rounded-full h-3 w-3 bg-destructive"></span>
                      </span>
                    )}
                    {/* Tooltip for icon-only view */}
                    <span className="lg:hidden absolute left-1/2 -translate-x-1/2 -bottom-12 px-3 py-1 bg-popover text-popover-foreground text-sm rounded-md opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-50">
                      {item.title}
                    </span>
                  </Link>
                ))}
              </div>
            </nav>

            {/* Right Side Items */}
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-2">
                <ModeToggle />
                <UserNav />
              </div>
              <MobileNav />
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}