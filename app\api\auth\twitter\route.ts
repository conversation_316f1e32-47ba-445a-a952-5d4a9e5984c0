import { NextResponse } from 'next/server';
import { redirect } from 'next/navigation';

export async function GET() {
  try {
    const clientId = process.env.NEXT_PUBLIC_TWITTER_CLIENT_ID;
    const redirectUri = `${process.env.NEXT_PUBLIC_BASE_URL}/api/auth/twitter/callback`;
    const scope = 'tweet.read%20users.read%20offline.access';
    const state = crypto.randomUUID();

    const authUrl = `https://twitter.com/i/oauth2/authorize?response_type=code&client_id=${clientId}&redirect_uri=${redirectUri}&scope=${scope}&state=${state}&code_challenge=challenge&code_challenge_method=plain`;

    return Response.redirect(authUrl);
  } catch (error) {
    console.error('Twitter auth error:', error);
    return Response.redirect(`${process.env.NEXT_PUBLIC_BASE_URL}/profile?error=twitter_auth_failed`);
  }
}
