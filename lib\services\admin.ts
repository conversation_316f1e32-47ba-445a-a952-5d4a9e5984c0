import { collection, query, where, getDocs, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { addDays, format, startOfDay, endOfDay } from 'date-fns';
import { tr } from 'date-fns/locale';

export interface DailyActivity {
  date: string;
  users: number;
  groups: number;
}

export const adminService = {
  async getActivityData(days: number = 7): Promise<DailyActivity[]> {
    const lastDays = Array.from({ length: days }, (_, i) => {
      const date = addDays(new Date(), -i);
      return {
        date: format(date, 'd MMM', { locale: tr }),
        start: startOfDay(date),
        end: endOfDay(date),
        users: 0,
        groups: 0
      };
    }).reverse();

    const usersRef = collection(db, 'users');
    const groupsRef = collection(db, 'groups');

    const activityData = await Promise.all(
      lastDays.map(async (day) => {
        const [userSnap, groupSnap] = await Promise.all([
          // Kullanıcı aktiviteleri
          getDocs(query(
            usersRef,
            where('lastLogin', '>=', Timestamp.fromDate(day.start)),
            where('lastLogin', '<=', Timestamp.fromDate(day.end))
          )),
          // Grup aktiviteleri
          getDocs(query(
            groupsRef,
            where('createdAt', '>=', Timestamp.fromDate(day.start)),
            where('createdAt', '<=', Timestamp.fromDate(day.end))
          ))
        ]);

        return {
          date: day.date,
          users: userSnap.size,
          groups: groupSnap.size
        };
      })
    );

    return activityData;
  },

  async getMetrics() {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const [
      usersSnap,
      activeUsersSnap,
      groupsSnap,
      reportsSnap,
      pendingReportsSnap
    ] = await Promise.all([
      getDocs(collection(db, 'users')),
      getDocs(query(
        collection(db, 'users'),
        where('lastLogin', '>=', Timestamp.fromDate(thirtyDaysAgo))
      )),
      getDocs(collection(db, 'groups')),
      getDocs(collection(db, 'reports')),
      getDocs(query(
        collection(db, 'reports'),
        where('status', '==', 'pending')
      ))
    ]);

    return {
      totalUsers: usersSnap.size,
      activeUsers: activeUsersSnap.size,
      totalGroups: groupsSnap.size,
      totalReports: reportsSnap.size,
      pendingReports: pendingReportsSnap.size
    };
  }
}; 