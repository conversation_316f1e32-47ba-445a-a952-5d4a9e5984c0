"use client";

import { useState, useEffect, useMemo } from "react";
import { collection, getDocs } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, UserCog, ChevronDown, Filter, ChevronUp, ChevronDown } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { formatDistanceToNow } from "date-fns";
import { tr } from "date-fns/locale";
import { User } from "../../types/user";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Checkbox } from "@/components/ui/checkbox";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { banUser, unbanUser,deleteUser } from "@/lib/firebase/users";

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10); // Made pageSize a state for future customization
  const [sortField, setSortField] = useState<keyof User | ''>('createdAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [statusFilter, setStatusFilter] = useState<User['status'] | 'all' | 'banned'>('all');
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [roleFilter, setRoleFilter] = useState<User['role'] | 'all'>('all'); // Added roleFilter state

  useEffect(() => {
    async function fetchUsers() {
      setLoading(true);
      try {
        const usersSnapshot = await getDocs(collection(db, "users"));
        const usersData = usersSnapshot.docs.map(doc => ({
          id: doc.id,
          email: doc.data().email || '',
          username: doc.data().username || '',
          status: doc.data().status || 'active',
          role: doc.data().role || 'user',
          nickname: doc.data().nickname,
          photoURL: doc.data().photoURL,
          createdAt: doc.data().createdAt?.toDate(),
          lastLogin: doc.data().lastLogin?.toDate(),
          banned: doc.data().banned || false, // Ensure banned has a default
          banReason: doc.data().banReason
        })) as User[];
        setUsers(usersData);
      } catch (error) {
        console.error("Error fetching users:", error);
        // Consider adding a toast notification for fetch error
      } finally {
        setLoading(false);
      }
    }
    fetchUsers();
  }, []); // Initial fetch

  const handleSort = (field: keyof User) => {
    if (sortField === field) {
      setSortDirection(prevDirection => (prevDirection === 'asc' ? 'desc' : 'asc'));
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const processedUsers = useMemo(() => {
    let mutableUsers = [...users];

    // Filtering by status and role
    mutableUsers = mutableUsers.filter(user => {
      if (statusFilter !== 'all') {
        if (statusFilter === 'banned' && !user.banned) return false;
        if (statusFilter === 'active' && (user.banned || user.status !== 'active')) return false;
        if (statusFilter === 'suspended' && (!user.status || user.status !== 'suspended')) return false;
      }
      if (roleFilter !== 'all' && user.role !== roleFilter) {
        return false;
      }
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        return (
          user.username?.toLowerCase().includes(query) ||
          user.email?.toLowerCase().includes(query) ||
          user.nickname?.toLowerCase().includes(query)
        );
      }
      return true;
    });

    // Sorting
    if (sortField) {
      mutableUsers.sort((a, b) => {
        const valA = a[sortField];
        const valB = b[sortField];

        let comparison = 0;
        if (valA === null || valA === undefined) comparison = -1;
        else if (valB === null || valB === undefined) comparison = 1;
        // Check for date objects and use getTime for comparison
        else if (valA instanceof Date && valB instanceof Date) {
            comparison = valA.getTime() > valB.getTime() ? 1 : (valA.getTime() < valB.getTime() ? -1 : 0);
        }
        else if (typeof valA === 'string' && typeof valB === 'string') {
            comparison = valA.localeCompare(valB);
        }
        // Fallback for other types (numbers, booleans)
        else if (valA > valB) comparison = 1;
        else if (valA < valB) comparison = -1;

        return sortDirection === 'asc' ? comparison : -comparison;
      });
    }

    return mutableUsers;
  }, [users, statusFilter, roleFilter, searchQuery, sortField, sortDirection]); // Added roleFilter to dependencies

  // Pagination logic should use processedUsers
  const paginatedUsers = useMemo(() => {
    const startIndex = (page - 1) * pageSize;
    return processedUsers.slice(startIndex, startIndex + pageSize);
  }, [processedUsers, page, pageSize]);

  const totalPages = Math.ceil(processedUsers.length / pageSize);

  const getStatusBadge = (user: User) => {
    let status = user.status;
    if (user.banned) status = 'banned';

    const statusConfig = {
      active: { color: 'bg-green-500', text: 'Aktif' },
      banned: { color: 'bg-red-500', text: 'Yasaklı' },
      suspended: { color: 'bg-yellow-500', text: 'Askıda' }
    };

    const config = statusConfig[status || 'active'];
    return (
      <div className="flex items-center gap-2">
        <div className={`h-2 w-2 rounded-full ${config.color}`}></div>
        <span className="text-sm">{config.text}</span>
      </div>
    );
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedUsers(processedUsers.map(user => user.id));
    } else {
      setSelectedUsers([]);
    }
  };

  const handleSelectUser = (userId: string, checked: boolean) => {
    if (checked) {
      setSelectedUsers(prev => [...prev, userId]);
    } else {
      setSelectedUsers(prev => prev.filter(id => id !== userId));
    }
  };

  const handleBulkAction = async (action: 'ban' | 'unban' | 'delete') => {
    if (!selectedUsers.length) return;

    try {
      switch (action) {
        case 'ban':
          await Promise.all(selectedUsers.map(id => banUser(id)));
          break;
        case 'unban':
          await Promise.all(selectedUsers.map(id => unbanUser(id)));
          break;
        case 'delete':
          await Promise.all(selectedUsers.map(id => deleteUser(id)));
          break;
      }
      // Kullanıcıları yeniden yükle
      const usersSnapshot = await getDocs(collection(db, "users"));
      const usersData = usersSnapshot.docs.map(doc => ({
        id: doc.id,
        email: doc.data().email || '',
        username: doc.data().username || '',
        status: doc.data().status || 'active',
        role: doc.data().role || 'user',
        nickname: doc.data().nickname,
        photoURL: doc.data().photoURL,
        createdAt: doc.data().createdAt?.toDate(),
        lastLogin: doc.data().lastLogin?.toDate(),
        banned: doc.data().banned,
        banReason: doc.data().banReason
      })) as User[];
      setUsers(usersData);
      setSelectedUsers([]);
    } catch (error) {
      console.error('Bulk action error:', error);
      alert('İşlem sırasında bir hata oluştu');
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Kullanıcılar</h1>
        {selectedUsers.length > 0 && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button>
                <UserCog className="w-4 h-4 mr-2" />
                Toplu İşlem ({selectedUsers.length})
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                    Seçilenleri Yasakla
                  </DropdownMenuItem>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Kullanıcıları Yasakla</AlertDialogTitle>
                    <AlertDialogDescription>
                      {selectedUsers.length} kullanıcıyı yasaklamak istediğinizden emin misiniz?
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>İptal</AlertDialogCancel>
                    <AlertDialogAction onClick={() => handleBulkAction('ban')}>
                      Yasakla
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>

              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                    Yasaklamaları Kaldır
                  </DropdownMenuItem>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Yasaklamaları Kaldır</AlertDialogTitle>
                    <AlertDialogDescription>
                      {selectedUsers.length} kullanıcının yasağını kaldırmak istediğinizden emin misiniz?
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>İptal</AlertDialogCancel>
                    <AlertDialogAction onClick={() => handleBulkAction('unban')}>
                      Yasağı Kaldır
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>

              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <DropdownMenuItem className="text-red-600" onSelect={(e) => e.preventDefault()}>
                    Seçilenleri Sil
                  </DropdownMenuItem>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Kullanıcıları Sil</AlertDialogTitle>
                    <AlertDialogDescription>
                      {selectedUsers.length} kullanıcıyı silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>İptal</AlertDialogCancel>
                    <AlertDialogAction onClick={() => handleBulkAction('delete')}>
                      Sil
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>

      <Card className="p-4">
        <div className="flex gap-4 mb-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Kullanıcı ara..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Filter className="w-4 h-4 mr-2" />
                Filtrele
                <ChevronDown className="w-4 h-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setStatusFilter('all')}>
                Tümü
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setStatusFilter('active')}>
                Aktif Kullanıcılar
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setStatusFilter('banned')}>
                Yasaklı Kullanıcılar
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setStatusFilter('suspended')}>
                Askıya Alınmış
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </Card>

      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={processedUsers.length > 0 && selectedUsers.length === processedUsers.length}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setSelectedUsers(processedUsers.map((user) => user.id));
                    } else {
                      setSelectedUsers([]);
                    }
                  }}
                />
              </TableHead>
              <TableHead>
                <Button variant="ghost" onClick={() => handleSort('username')}>
                  Kullanıcı
                  {sortField === 'username' && (sortDirection === 'asc' ? <ChevronUp className="inline h-4 w-4 ml-1" /> : <ChevronDown className="inline h-4 w-4 ml-1" />)}
                </Button>
              </TableHead>
              <TableHead>
                <Button variant="ghost" onClick={() => handleSort('email')}>
                  E-posta
                  {sortField === 'email' && (sortDirection === 'asc' ? <ChevronUp className="inline h-4 w-4 ml-1" /> : <ChevronDown className="inline h-4 w-4 ml-1" />)}
                </Button>
              </TableHead>
              <TableHead>
                <Button variant="ghost" onClick={() => handleSort('createdAt')}>
                  Kayıt Tarihi
                  {sortField === 'createdAt' && (sortDirection === 'asc' ? <ChevronUp className="inline h-4 w-4 ml-1" /> : <ChevronDown className="inline h-4 w-4 ml-1" />)}
                </Button>
              </TableHead>
              <TableHead>
                <Button variant="ghost" onClick={() => handleSort('lastLogin')}>
                  Son Giriş
                  {sortField === 'lastLogin' && (sortDirection === 'asc' ? <ChevronUp className="inline h-4 w-4 ml-1" /> : <ChevronDown className="inline h-4 w-4 ml-1" />)}
                </Button>
              </TableHead>
              <TableHead>Durum</TableHead>
              <TableHead>Rol</TableHead> {/* Added Role header */}
              <TableHead className="text-right">İşlemler</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center">
                  <div className="flex items-center justify-center py-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                </TableCell>
              </TableRow>
            ) : processedUsers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-4">
                  Kullanıcı bulunamadı
                </TableCell>
              </TableRow>
            ) : (
              paginatedUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedUsers.includes(user.id)}
                      onCheckedChange={(checked) => handleSelectUser(user.id, checked as boolean)}
                    />
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={user.photoURL} />
                        <AvatarFallback>{user.username?.[0]}</AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{user.nickname || user.username}</p>
                        <p className="text-sm text-muted-foreground">@{user.username}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>
                    {user.createdAt ? formatDistanceToNow(user.createdAt, { addSuffix: true, locale: tr }) : '-'}
                  </TableCell>
                  <TableCell>
                    {user.lastLogin ? formatDistanceToNow(user.lastLogin, { addSuffix: true, locale: tr }) : '-'}
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(user)}
                  </TableCell>
                  <TableCell>{user.role}</TableCell> {/* Added Role cell */}
                  <TableCell className="text-right">
                    <Button variant="outline" size="sm" onClick={() => window.location.href = `/admin/users/${user.id}`}>Detaylar</Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </Card>

      {totalPages > 1 && (
        <div className="flex items-center justify-between px-4 py-3 border-t">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage(p => Math.max(1, p - 1))}
            disabled={page === 1}
          >
            Önceki
          </Button>
          <div className="text-sm text-muted-foreground">
            Sayfa {page} / {totalPages}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage(p => Math.min(totalPages, p + 1))}
            disabled={page === totalPages}
          >
            Sonraki
          </Button>
        </div>
      )}
    </div>
  );
}
