"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { doc, getDoc, collection, query, where, getDocs } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { Card } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Trophy } from "lucide-react";
import { useAuth } from "@/lib/hooks/useAuth";

interface UserProfileProps {
  username: string;
}

export default function UserProfile({ username }: UserProfileProps) {
  const router = useRouter();
  const { user: currentUser } = useAuth();
  const [profileUser, setProfileUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        const usersRef = collection(db, "users");
        const q = query(usersRef, where("username", "==", username));
        const querySnapshot = await getDocs(q);

        if (querySnapshot.empty) {
          setError("Kullanıcı bulunamadı");
          setLoading(false);
          return;
        }

        const userDoc = querySnapshot.docs[0];
        const userData = userDoc.data();

        // Gizlilik kontrolü
        if (!userData.isPublic && (!currentUser || currentUser.uid !== userDoc.id)) {
          setError("Bu profil gizli");
          setLoading(false);
          return;
        }

        setProfileUser(userData);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching user profile:", error);
        setError("Profil yüklenirken bir hata oluştu");
        setLoading(false);
      }
    };

    if (username) {
      fetchUserProfile();
    }
  }, [username, currentUser]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center w-full">
        <div className="w-full max-w-6xl mx-auto py-10 px-4 sm:px-6 lg:px-8">
          <Card className="p-6 text-center">
            <h1 className="text-xl font-bold text-destructive mb-2">{error}</h1>
            <p className="text-muted-foreground">
              {error === "Bu profil gizli" 
                ? "Bu kullanıcı profilini gizli tutmayı tercih ediyor."
                : "Aradığınız kullanıcı bulunamadı."}
            </p>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="flex justify-center w-full">
      <div className="w-full max-w-6xl mx-auto py-10 px-4 sm:px-6 lg:px-8">
        {/* Profil Kartı */}
        <Card className="mb-8">
          <div className="flex flex-col sm:flex-row items-center p-6 gap-6">
            <div className="flex items-center gap-6">
              <div className="relative">
                <Avatar className="h-24 w-24">
                  <AvatarImage src={profileUser?.photoURL} />
                  <AvatarFallback>
                    {profileUser?.nickname?.charAt(0) || profileUser?.username?.charAt(0)}
                  </AvatarFallback>
                </Avatar>
              </div>
              <div>
                <h2 className="text-xl font-bold">{profileUser?.nickname || profileUser?.username}</h2>
                <p className="text-muted-foreground">Üye</p>
                <div className="mt-2">
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary/20 text-primary">
                    <Trophy className="w-4 h-4 mr-1" />
                    Seviye 1
                  </span>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
} 