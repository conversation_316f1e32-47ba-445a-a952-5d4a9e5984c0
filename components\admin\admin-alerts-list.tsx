"use client";

import { useState, useEffect } from "react";
import { collection, query, orderBy, limit, getDocs, Timestamp } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { formatDistanceToNow } from "date-fns";
import { tr } from "date-fns/locale";
import { AlertTriangle, Ban, Flag, UserX } from "lucide-react";
import { Button } from "@/components/ui/button";

interface Alert {
  id: string;
  type: "spam" | "abuse" | "violation" | "other";
  message: string;
  timestamp: Date;
  status: "pending" | "resolved";
}

const alertIcons = {
  spam: Flag,
  abuse: UserX,
  violation: AlertTriangle,
  other: Ban,
};

export function AdminAlertsList() {
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchAlerts() {
      try {
        const alertsQuery = query(
          collection(db, "alerts"),
          orderBy("timestamp", "desc"),
          limit(5)
        );
        const snapshot = await getDocs(alertsQuery);
        const alertsData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          timestamp: (doc.data().timestamp as Timestamp)?.toDate() || new Date(),
        })) as Alert[];
        setAlerts(alertsData);
      } catch (error) {
        console.error("Error fetching alerts:", error);
      } finally {
        setLoading(false);
      }
    }

    fetchAlerts();
  }, []);

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="h-16 bg-muted rounded-lg"></div>
          </div>
        ))}
      </div>
    );
  }

  if (alerts.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        <AlertTriangle className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
        <p>Henüz bir uyarı bulunmuyor</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {alerts.map((alert) => {
        const Icon = alertIcons[alert.type];
        return (
          <div
            key={alert.id}
            className="flex items-start justify-between p-4 rounded-lg border bg-card text-card-foreground"
          >
            <div className="flex gap-3">
              <Icon className="w-5 h-5 text-destructive mt-1" />
              <div>
                <p className="font-medium">{alert.message}</p>
                <p className="text-sm text-muted-foreground">
                  {formatDistanceToNow(alert.timestamp, {
                    addSuffix: true,
                    locale: tr,
                  })}
                </p>
              </div>
            </div>
            <Button variant="outline" size="sm">
              İncele
            </Button>
          </div>
        );
      })}
    </div>
  );
}