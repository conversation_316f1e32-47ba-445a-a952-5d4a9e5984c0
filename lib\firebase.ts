import { initializeApp, getApps } from 'firebase/app';
import { getAuth, onAuthStateChanged } from 'firebase/auth';
import { getFirestore, getDoc, setDoc, doc } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import { getAnalytics, isSupported } from 'firebase/analytics';

const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID,
};

// Initialize Firebase
const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];
const auth = getAuth(app);
const db = getFirestore(app);
const storage = getStorage(app);

// Initialize Analytics conditionally (only in browser)
let analytics = null;
if (typeof window !== 'undefined') {
  isSupported().then(yes => yes && (analytics = getAnalytics(app)));
}

// Kullanıcı dökümanını oluştur veya kontrol et
export async function ensureUserDocument(user: any) {
  const userRef = doc(db, 'users', user.uid);
  const userDoc = await getDoc(userRef);

  if (!userDoc.exists()) {
    await setDoc(userRef, {
      uid: user.uid,
      email: user.email,
      nickname: user.displayName || user.email?.split('@')[0],
      photoURL: user.photoURL || `https://api.dicebear.com/7.x/avataaars/svg?seed=${user.uid}`,
      createdAt: new Date(),
      isPublic: true,
      linkedAccounts: {},
    });
  }

  return userDoc;
}

// Auth state değişikliklerini dinle
onAuthStateChanged(auth, async (user) => {
  if (user) {
    // Kullanıcının Firestore dökümanını kontrol et
    const userRef = doc(db, 'users', user.uid);
    const userDoc = await getDoc(userRef);

    // Döküman yoksa oluştur
    if (!userDoc.exists()) {
      await setDoc(userRef, {
        uid: user.uid,
        email: user.email,
        nickname: user.displayName || user.email?.split('@')[0],
        photoURL: user.photoURL || `https://api.dicebear.com/7.x/avataaars/svg?seed=${user.uid}`,
        createdAt: new Date(),
        isPublic: true,
        linkedAccounts: {},
      });
    }
  }
});

export const saveSettingsToFirebase = async (settings: { registrationEnabled: boolean; maintenanceMode: boolean; [key: string]: any; }) => {
    const db = getFirestore();
    const settingsRef = doc(db, "settings", "siteSettings");
    await setDoc(settingsRef, settings);
};

export const getSettingsFromFirebase = async () => {
    const db = getFirestore();
    const settingsRef = doc(db, "settings", "siteSettings");
    const settingsDoc = await getDoc(settingsRef);
    
    if (settingsDoc.exists()) {
        return settingsDoc.data();
    } else {
        console.log("No such document!");
        return null;
    }
};

export { app, auth, db, storage, analytics };