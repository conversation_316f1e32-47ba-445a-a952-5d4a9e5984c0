"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { isValidUsername } from "@/lib/utils/validation";
import { isUsernameTaken } from "@/lib/firebase/auth";
import { createUserWithEmailAndPassword, updateProfile } from "firebase/auth";
import { doc, setDoc } from "firebase/firestore";
import { auth, db } from "@/lib/firebase";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";

export default function SignUpForm() {
  const router = useRouter();
  const { toast } = useToast();
  const [error, setError] = useState("");
  const [usernameError, setUsernameError] = useState("");
  const [loading, setLoading] = useState(false);

  async function handleSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault();
    setError("");
    setUsernameError("");
    setLoading(true);

    const formData = new FormData(event.currentTarget);
    const username = formData.get("username") as string;
    const email = formData.get("email") as string;
    const password = formData.get("password") as string;
    
    // Validate username format
    const validation = isValidUsername(username);
    if (!validation.valid) {
      setUsernameError(validation.message || "Geçersiz kullanıcı adı");
      setLoading(false);
      return;
    }
    
    try {
      // Check if username is taken
      const taken = await isUsernameTaken(username);
      if (taken) {
        setUsernameError("Bu kullanıcı adı başka bir hesap tarafından kullanılıyor");
        setLoading(false);
        return;
      }

      // Create user with email and password
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      
      // Set initial display name as username
      await updateProfile(userCredential.user, {
        displayName: username
      });

      // Create user document in Firestore
      await setDoc(doc(db, "users", userCredential.user.uid), {
        id: userCredential.user.uid,
        username: username.toLowerCase(), // Immutable username
        nickname: username, // Initial nickname same as username
        email,
        groups: [],
        pendingRequests: [],
        createdAt: new Date(),
        lastLogin: new Date(),
        isPublic: false, // Varsayılan olarak gizli profil
        photoURL: `https://api.dicebear.com/7.x/avataaars/svg?seed=${userCredential.user.uid}`
      });

      toast({
        title: "Hesap oluşturuldu",
        description: "Başarıyla kayıt oldunuz!",
      });

      router.push("/profile");
    } catch (err) {
      setError("Kayıt olurken bir hata oluştu. Lütfen tekrar deneyin.");
      console.error(err);
    } finally {
      setLoading(false);
    }
  }

  return (
    <div className="container max-w-md mx-auto py-16 px-4">
      <Card className="p-6">
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold">Hesap Oluştur</h1>
          <p className="text-muted-foreground mt-2">
            Oyuncu Bul'a hoş geldin! Hemen kayıt ol ve oyun arkadaşlarını bulmaya başla.
          </p>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        {usernameError && (
          <Alert variant="destructive" className="mb-6">
            <AlertDescription>{usernameError}</AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="username">Kullanıcı Adı</Label>
            <Input
              id="username"
              name="username"
              type="text"
              required
              placeholder="Kullanıcı adınız"
            />
            <p className="text-xs text-muted-foreground">
              Bu kullanıcı adı benzersiz kimliğiniz olacak ve değiştirilemez.
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">E-posta</Label>
            <Input
              id="email"
              name="email"
              type="email"
              required
              placeholder="<EMAIL>"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="password">Şifre</Label>
            <Input
              id="password"
              name="password"
              type="password"
              required
              placeholder="********"
              minLength={6}
            />
          </div>

          <Button type="submit" className="w-full" disabled={loading}>
            {loading ? "Kayıt olunuyor..." : "Kayıt Ol"}
          </Button>
        </form>

        <p className="text-center text-sm mt-6">
          Zaten hesabın var mı?{" "}
          <Link href="/auth/sign-in" className="text-primary hover:underline">
            Giriş yap
          </Link>
        </p>
      </Card>
    </div>
  );
} 