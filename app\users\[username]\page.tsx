import { getUserProfileByUsername } from '@/lib/firebase/user';
import { notFound } from 'next/navigation';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Suspense, cache } from 'react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { Users } from 'lucide-react';
import { FaGoogle, FaTwitch, FaDiscord, FaTwitter } from "react-icons/fa";
import { ClientMessageButton } from '@/components/user/client-message-button';
import { Metadata } from 'next'

const getUserProfile = cache(async (username: string) => {
  return await getUserProfileByUsername(username);
});

async function UserProfile({ params }: { params: { username: string } }) {
  const { username } = await params;
  const user = await getUserProfile(username);

  if (!user) {
    notFound();
  }
  const publicAccounts = user.linkedAccounts ? Object.entries(user.linkedAccounts)
    .filter(([_, account]) => account.linked && account.isPublic)
    .map(([platform, account]) => ({ platform, ...account })) : [];

  return (
    <div className="max-w-6xl mx-auto space-y-4 pt-3">
      <Card className="bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Avatar className="h-16 w-16">
                <AvatarImage src={user.photoURL || ''} alt={user.displayName} />
                <AvatarFallback>{user.displayName?.[0]}</AvatarFallback>
              </Avatar>
              <div>
                <h2 className="text-xl font-semibold">{user.displayName}</h2>
                <p className="text-muted-foreground">Üye</p>
                <div className="flex items-center gap-1 mt-1">
                  <svg viewBox="0 0 24 24" className="w-4 h-4 text-muted-foreground" fill="currentColor">
                    <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" />
                  </svg>
                  <span className="text-sm text-muted-foreground">Seviye 1</span>
                </div>
              </div>
            </div>
            
            <ClientMessageButton 
              userId={user.id} 
              username={user.username || user.displayName} 
            />
          </div>
        </CardContent>
      </Card>

      {publicAccounts.length > 0 && (
        <Card className="bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <CardContent className="p-6">
            <div className="flex flex-wrap gap-4">
              {publicAccounts.map(({ platform, data }) => (
                <div key={platform} className="flex items-center gap-2 bg-secondary/50 rounded-full px-3 py-1.5">
                  {platform === 'google' && <FaGoogle className="w-4 h-4" />}
                  {platform === 'twitch' && <FaTwitch className="w-4 h-4" />}
                  {platform === 'discord' && <FaDiscord className="w-4 h-4" />}
                  {platform === 'twitter' && <FaTwitter className="w-4 h-4" />}
                  <span className="text-sm">
                    {platform === 'google' && data?.name}
                    {platform === 'twitch' && data?.username}
                    {platform === 'discord' && data?.username}
                    {platform === 'twitter' && data?.username}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
      <Card className="bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <CardContent className="p-6">
          <div className="flex items-center gap-2 mb-4">
            <Users className="w-5 h-5" />
            <h2 className="text-lg font-semibold">Katıldığı Gruplar</h2>
          </div>
          {user.groups && user.groups.length > 0 ? (
            <div className="space-y-2">
              {user.groups.map((group) => (
                <Link key={group.id} href={`/groups/${group.id}`}>
                  <Button 
                    variant="secondary" 
                    className="w-full justify-start bg-secondary/50 hover:bg-secondary/70"
                  >
                    <Users className="w-4 h-4 mr-2" />
                    {group.name}
                  </Button>
                </Link>
              ))}
            </div>
          ) : (
            <div className="rounded-lg border border-dashed p-6 flex items-center justify-center">
              <p className="text-muted-foreground">Henüz bir gruba katılmamış.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

function UserProfileSkeleton() {
  return (
    <div className="max-w-6xl mx-auto space-y-4">
      <Card className="bg-background/95">
        <CardContent className="p-6">
          <div className="flex items-center gap-4">
            <Skeleton className="h-16 w-16 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-4 w-24" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function UserProfilePage({ params }: { params: { username: string } }) {
  return (
    <Suspense fallback={<UserProfileSkeleton />}>
      <UserProfile params={params} />
    </Suspense>
  );
}

export async function generateMetadata({ params }: { params: { username: string } }): Promise<Metadata> {
  const { username } = await params;
  const user = await getUserProfile(username);

  if (!user) {
    return {
      title: 'Kullanıcı Bulunamadı',
      description: 'Aradığınız kullanıcı bulunamadı.'
    }
  }

  return {
    title: `${user.displayName} (@${user.username})`,
    description: user.bio || 'Oyuncu Bul kullanıcı profili',
    openGraph: {
      title: `${user.displayName} (@${user.username})`,
      description: user.bio || "",
      images: [
        {
          url: user.photoURL || '/default-avatar.png',
          width: 1200,
          height: 630,
          alt: user.displayName
        }
      ]
    },
    twitter: {
      card: 'summary_large_image',
      title: user.displayName, 
      description: user.bio || 'Oyuncu Bul\'da bir kullanıcı',
      images: [user.photoURL || '/default-avatar.png'], 
      creator: '@oyuncubul'
    }
  }
}