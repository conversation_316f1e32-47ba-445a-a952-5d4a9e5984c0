import { useQuery } from "@tanstack/react-query";
import { doc, onSnapshot } from "firebase/firestore";
import { db } from "../firebase";
import { Group } from "@/lib/types/firebase";

export function useGroupDetails(groupId: string) {
  return useQuery<Group>({
    queryKey: ['group', groupId],
    queryFn: () => new Promise((resolve) => {
      const unsubscribe = onSnapshot(
        doc(db, 'groups', groupId),
        (doc) => {
          resolve({ id: doc.id, ...doc.data() } as Group);
        }
      );
      return () => unsubscribe();
    }),
  });
} 