"use client"

import { useTheme } from "@/components/theme-provider"
import { Moon, Sun } from "lucide-react"
import { useEffect, useState } from "react"

export function ThemeToggle() {
  const { theme, setTheme } = useTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    // Sunucu tarafında ve ilk render'da varsayılan görünüm
    return (
      <button className="relative h-6 w-6">
        <Sun className="absolute h-full w-full" />
      </button>
    )
  }

  const isDark = theme?.includes('-dark')
  const baseTheme = isDark ? theme?.replace('-dark', '') : theme

  return (
    <button
      onClick={() => setTheme(isDark ? baseTheme! : `${baseTheme}-dark`)}
      className="relative h-6 w-6"
      suppressHydrationWarning
    >
      <Sun 
        className={`absolute h-full w-full transition-all ${
          isDark ? 'scale-0 opacity-0' : 'scale-100 opacity-100'
        }`}
      />
      <Moon 
        className={`absolute h-full w-full transition-all ${
          isDark ? 'scale-100 opacity-100' : 'scale-0 opacity-0'
        }`}
      />
    </button>
  )
} 