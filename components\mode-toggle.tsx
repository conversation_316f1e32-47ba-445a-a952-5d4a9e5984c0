"use client";

import { <PERSON>, Sun } from "lucide-react";
import { useTheme } from "next-themes";
import { Button } from "@/components/ui/button";

export function ModeToggle() {
  const { theme, setTheme } = useTheme();

  const toggleDarkMode = () => {
    document.documentElement.classList.add('theme-transition');
    
    const currentTheme = theme?.replace('-dark', '');
    if (theme?.includes('-dark')) {
      setTheme(currentTheme || 'blue');
    } else {
      setTheme(`${currentTheme || 'blue'}-dark`);
    }

    setTimeout(() => {
      document.documentElement.classList.remove('theme-transition');
    }, 300);
  };

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleDarkMode}
    >
      <div className="relative w-[1.2rem] h-[1.2rem]">
        <Sun 
          className={`absolute h-full w-full transition-transform duration-500 ${
            theme?.includes('-dark') ? 'opacity-0 rotate-90' : 'opacity-100 rotate-0'
          }`} 
        />
        <Moon 
          className={`absolute h-full w-full transition-transform duration-500 ${
            theme?.includes('-dark') ? 'opacity-100 rotate-0' : 'opacity-0 -rotate-90'
          }`}
        />
      </div>
      <span className="sr-only">Temayı değiştir</span>
    </Button>
  );
}