import { User as FirebaseUser } from 'firebase/auth';
import { Key } from 'react';

export interface Group {
  tags?: string[];
  id: string;
  name: string;
  description: string;
  category: string;
  platform: string;
  isActive: boolean;
  ownerId: string;
  members: string[];
  admins: string[];
  pendingRequests: string[];
  memberCount: number;
  createdAt: Date;
  updatedAt: Date;
  type: 'public' | 'private';
  image?: string;
}

export interface User extends FirebaseUser {
  id: string;
  username: string;
  role: string;
  lastLogin: Date;
  nickname?: string;
  // ... diğer custom alanlar
}

export type AdminRole = 'superadmin' | 'admin' | 'moderator';
export type AdminStatus = 'active' | 'inactive' | 'suspended';
export type AdminPermission = 
  | 'all'
  | 'users.manage'
  | 'users.view'
  | 'groups.manage'
  | 'groups.view'
  | 'reports.manage'
  | 'reports.view'
  | 'settings.manage';

export interface SuperAdmin {
  uid: string;
  email: string;
  fullName: string;
  role: AdminRole;
  status: AdminStatus;
  permissions: AdminPermission[];
  lastLogin: Date;
  createdAt: Date;
  updatedAt: Date;
  ipWhitelist: string[];
  mfaEnabled: boolean;
  loginAttempts: number;
  lastIp: string;
  lastUserAgent: string;
}

export type AdminActionType = 
  | 'BAN_USER' 
  | 'UNBAN_USER' 
  | 'CLOSE_GROUP' 
  | 'DELETE_GROUP' 
  | 'RESOLVE_REPORT' 
  | 'UPDATE_SETTINGS';

export type AdminTargetType = 'user' | 'group' | 'report' | 'system';

export interface AdminLog {
  id?: string;
  adminId: string;
  action: AdminActionType;
  targetType: AdminTargetType;
  targetId: string;
  timestamp?: Date;
  ip?: string;
  userAgent?: string;
  status: 'success' | 'error';
  details?: Record<string, any>;
}

export interface AdminCommand {
  type: AdminActionType;
  targetId: string;
  params?: Record<string, any>;
}

export interface AdminLogFilters {
  adminId?: string;
  targetType?: AdminTargetType;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
}

export interface GroupRequest {
  id: string;
  userId: string;
  groupId: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: Date;
  processedAt?: Date;
}

export interface Message {
  id: string;
  content: string;
  senderId: string;
  createdAt: any;
  read: boolean;
}

export interface Chat {
  id: string;
  participants: string[];
  messages: Message[];
  lastMessage?: Message;
  createdAt: any;
  updatedAt: any;
}

export interface UserProfile {
  id: string;
  username: string;
  nickname?: string;
  photoURL?: string;
  isPublic: boolean;
  createdAt: Date;
  twitchId?: string;
  twitchUsername?: string;
  twitchEmail?: string;
}

export interface TwitchConnection {
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
}

type LinkedAccountData = {
  google: {
    linked: boolean;
    data: { email?: string } | null;
  };
  twitch: {
    linked: boolean;
    data: { username?: string } | null;
  };
  discord: {
    linked: boolean;
    data: { username?: string } | null;
  };
};

export interface Moderator {
  id: string;
  userId: string;
  email: string;
  displayName: string;
  role: 'moderator';
  status: 'active' | 'inactive' | 'suspended';
  permissions: AdminPermission[];
  assignedBy: string;
  assignedAt: Date;
  lastActive?: Date;
  assignedGroups?: string[];
  assignedCategories?: string[];
  notes?: string;
}

export interface ModeratorStats {
  totalActions: number;
  resolvedReports: number;
  bannedUsers: number;
  lastActionAt?: Date;
}

export interface GroupMember {
  id: string;
  username: string;
  role: string;
  lastLogin: Date | null;
  isOnline?: boolean;
  nickname?: string;
  photoURL?: string;
}