import { useState, useEffect } from 'react';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { User } from '@/lib/types/firebase';

interface GroupMember extends Pick<User, 'id' | 'username' | 'lastLogin' | 'role'> {
  isOnline?: boolean;
  nickname?: string;
  photoURL?: string;
}

const convertFirebaseTimestamp = (timestamp: any): Date | null => {
  if (!timestamp) return null;
  
  // Firestore Timestamp objesi ise
  if (timestamp.seconds) {
    return new Date(timestamp.seconds * 1000);
  }
  
  // Zaten Date objesi ise
  if (timestamp instanceof Date) {
    return timestamp;
  }
  
  // String ise
  if (typeof timestamp === 'string') {
    return new Date(timestamp);
  }
  
  return null;
};

export function useGroupMembers(groupId: string) {
  const [members, setMembers] = useState<GroupMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    async function fetchMembers() {
      try {
        setLoading(true);
        const groupDoc = await getDoc(doc(db, 'groups', groupId));
        const groupData = groupDoc.data();

        if (!groupData) {
          throw new Error('Grup bulunamadı');
        }

        const memberPromises = groupData.members.map(async (memberId: string) => {
          const userDoc = await getDoc(doc(db, 'users', memberId));
          const userData = userDoc.data();

          if (!userData) return null;

          return {
            id: userDoc.id,
            username: userData.username,
            nickname: userData.nickname || userData.username,
            lastLogin: convertFirebaseTimestamp(userData.lastLogin),
            role: userData.role,
            photoURL: userData.photoURL,
            isOnline: userData.lastLogin ? Date.now() - convertFirebaseTimestamp(userData.lastLogin)!.getTime() < 5 * 60 * 1000 : false
          };
        });

        const memberData = (await Promise.all(memberPromises)).filter(Boolean) as GroupMember[];
        
        const sortedMembers = memberData.sort((a, b) => {
          if (a.role === 'admin') return -1;
          if (b.role === 'admin') return 1;
          if (a.isOnline && !b.isOnline) return -1;
          if (!a.isOnline && b.isOnline) return 1;
          return 0;
        });

        setMembers(sortedMembers);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Üyeler yüklenirken bir hata oluştu'));
      } finally {
        setLoading(false);
      }
    }

    fetchMembers();
  }, [groupId]);

  return { members, loading, error };
} 