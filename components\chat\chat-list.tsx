"use client";

import { useMessages } from '@/lib/hooks/useMessages';
import { useAuth } from '@/lib/hooks/useAuth';
import { useState, useEffect } from 'react';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Card } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { formatDistanceToNow } from 'date-fns';
import { tr } from 'date-fns/locale';
import Link from 'next/link';
import { Timestamp } from 'firebase/firestore';

interface UserInfo {
  id: string;
  nickname: string;
  username: string;
  photoURL: string | null;
}

interface ChatMessage {
  content: string;
  senderId: string;
  createdAt: Timestamp;
}

export function ChatList() {
  const { user } = useAuth();
  const { chats, loading, error } = useMessages(user?.uid || '');
  const [userInfoMap, setUserInfoMap] = useState<Record<string, UserInfo>>({});

  useEffect(() => {
    async function fetchUserInfo(userId: string) {
      try {
        const userDoc = await getDoc(doc(db, 'users', userId));
        if (userDoc.exists()) {
          const userData = userDoc.data();
          setUserInfoMap(prev => ({
            ...prev,
            [userId]: {
              id: userId,
              nickname: userData.nickname || 'Anonim Kullanıcı',
              username: userData.username,
              photoURL: userData.photoURL
            }
          }));
        }
      } catch (error) {
        console.error('Error fetching user info:', error);
      }
    }

    // Fetch user info for all participants
    chats.forEach(chat => {
      const otherParticipant = chat.participants.find(id => id !== user?.uid);
      if (otherParticipant && !userInfoMap[otherParticipant]) {
        fetchUserInfo(otherParticipant);
      }
    });
  }, [chats, user?.uid, userInfoMap]);

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <Card key={i} className="p-4 animate-pulse">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 rounded-full bg-muted" />
              <div className="flex-1">
                <div className="h-4 bg-muted rounded w-1/4 mb-2" />
                <div className="h-3 bg-muted rounded w-3/4" />
              </div>
            </div>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card className="p-6 text-center">
        <p className="text-muted-foreground">
          Mesajlar yüklenirken bir hata oluştu.
        </p>
      </Card>
    );
  }

  if (chats.length === 0) {
    return (
      <Card className="p-6 text-center">
        <p className="text-muted-foreground">
          Henüz hiç mesajınız yok.
        </p>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {chats.map((chat) => {
        const otherParticipant = chat.participants.find(id => id !== user?.uid);
        if (!otherParticipant) return null;
        
        const otherUser = userInfoMap[otherParticipant];
        if (!otherUser) return null;

        return (
          <Link key={chat.id} href={`/messages/${chat.id}`}>
            <Card className="p-4 hover:bg-muted/50 transition-colors">
              <div className="flex items-center gap-4">
                <Avatar>
                  <AvatarImage src={otherUser.photoURL || `https://api.dicebear.com/7.x/avataaars/svg?seed=${otherParticipant}`} />
                  <AvatarFallback>{otherUser.nickname[0]}</AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="font-medium truncate">{otherUser.nickname}</p>
                    {chat.lastMessage && (
                      <span className="text-xs text-muted-foreground">
                        {formatDistanceToNow(
                          chat.lastMessage.createdAt instanceof Timestamp 
                            ? chat.lastMessage.createdAt.toDate() 
                            : new Date(chat.lastMessage.createdAt),
                          {
                            addSuffix: true,
                            locale: tr
                          }
                        )}
                      </span>
                    )}
                  </div>
                  {chat.lastMessage && (
                    <p className="text-sm text-muted-foreground truncate">
                      {chat.lastMessage.senderId === user?.uid ? 'Sen: ' : ''}
                      {chat.lastMessage.content}
                    </p>
                  )}
                </div>
              </div>
            </Card>
          </Link>
        );
      })}
    </div>
  );
}