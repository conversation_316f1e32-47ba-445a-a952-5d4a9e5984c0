"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/lib/hooks/useAuth";
import { doc, setDoc, getDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { Loader2 } from "lucide-react";
import type { SuperAdmin } from "@/lib/types/firebase";

export default function SuperAdminRegistration() {
  const { user } = useAuth();
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [secretKey, setSecretKey] = useState("");
  const [error, setError] = useState<string | null>(null);

  const handleRegistration = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      // Verify secret key (this should match your environment variable)
      if (secretKey !== process.env.NEXT_PUBLIC_SUPER_ADMIN_KEY) {
        throw new Error("Geçersiz güvenlik anahtarı");
      }

      // Check if user is already a super admin
      const adminDoc = await getDoc(doc(db, "superadmins", user.uid));
      if (adminDoc.exists()) {
        throw new Error("Bu hesap zaten süper admin");
      }

      // Create super admin document
      const adminData: SuperAdmin = {
        uid: user.uid,
        email: user.email!,
        fullName: user.displayName || "Admin",
        role: "superadmin",
        status: "active",
        permissions: ["all"],
        lastLogin: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
        ipWhitelist: [],
        mfaEnabled: false,
        loginAttempts: 0,
        lastIp: "",
        lastUserAgent: navigator.userAgent
      };

      await setDoc(doc(db, "superadmins", user.uid), adminData);

      toast({
        title: "Başarılı",
        description: "Süper admin kaydınız oluşturuldu!",
      });

      // Use router for client-side navigation
      router.push("/admin");
    } catch (error) {
      setError(error instanceof Error ? error.message : "Bir hata oluştu");
      toast({
        title: "Hata",
        description: error instanceof Error ? error.message : "Kayıt sırasında bir hata oluştu",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="container max-w-md mx-auto py-16 px-4">
        <Card className="p-6 text-center">
          <p className="text-muted-foreground">
            Süper admin kaydı için giriş yapmanız gerekiyor.
          </p>
          <Button
            className="mt-4"
            variant="outline"
            onClick={() => router.push("/auth/sign-in")}
          >
            Giriş Yap
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="container max-w-md mx-auto py-16 px-4">
      <Card className="p-6">
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold">Süper Admin Kaydı</h1>
          <p className="text-muted-foreground mt-2">
            Güvenlik anahtarını girerek süper admin olarak kayıt olun
          </p>
        </div>

        {error && (
          <div className="bg-destructive/10 text-destructive px-4 py-2 rounded-md mb-4">
            {error}
          </div>
        )}

        <form onSubmit={handleRegistration} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="secretKey">Güvenlik Anahtarı</Label>
            <Input
              id="secretKey"
              type="password"
              value={secretKey}
              onChange={(e) => setSecretKey(e.target.value)}
              required
              placeholder="••••••••"
            />
          </div>

          <Button type="submit" className="w-full" disabled={loading}>
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Kaydediliyor...
              </>
            ) : (
              "Kayıt Ol"
            )}
          </Button>
        </form>
      </Card>
    </div>
  );
}